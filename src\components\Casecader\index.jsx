import { ProFormCascader, ProFormField } from '@ant-design/pro-components'
import { Cascader } from 'antd-mobile'
import { useEffect, useState } from 'react'
import REGION_LIST from '@/assets/city-options/list.json'

const CascaderMp = ({ placeholder, label, value, onChange, ...rest }) => {
  const options = rest?.fieldProps?.options
  const [regionName, setRegionName] = useState('')
  useEffect(() => {
    if (value?.length) {
      setRegionName(
        value
          ?.map((el) => {
            return REGION_LIST[el]
          })
          .join('-'),
      )
    }
  }, [value])
  return (
    <ProFormField
      noStyle
      width="md"
      value={regionName}
      placeholder={placeholder}
      fieldProps={{
        readOnly: true,
        onClick: async () => {
          const value = await Cascader.prompt({
            options,
            title: '所在区域',
          })
          if (value.length) {
            onChange(value)
          }
        },
      }}
    />
  )
}

export default process.env.PUBLIC_H5 ? CascaderMp : ProFormCascader
