import { ProTable } from '@ant-design/pro-components'
import { usePagination } from 'ahooks'

const TableRender = ({ data, columns }) => {
  const { data: dataSource, pagination } = usePagination(
    ({ current, pageSize }) => {
      return {
        list: data?.slice((current - 1) * pageSize, current * pageSize),
        total: data?.length,
      }
    },
    {
      defaultPageSize: 10,
    },
  )

  return (
    <ProTable
      rowKey="id"
      className="w-[85%]"
      columns={columns}
      dataSource={dataSource?.list}
      expandable={false}
      toolBarRender={false}
      search={false}
      pagination={{
        ...pagination,
        showSizeChanger: false,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`,
        hideOnSinglePage: true,
      }}
      scroll={{ x: 'max-content' }}
    />
  )
}

export default TableRender
