import React, { useState, useEffect } from 'react'
import { Card, Tag, Tooltip } from 'antd'
import { useParams, useHistory } from 'react-router-dom'
import GroupedTable from '@/components/GroupedTable'
import WithWatermark from '../components/WithWatermark'

import { PROJECT_STATUS_CONFIG } from 'views/dataDashboard/config'
import { getProjectList93656 } from '@/apis/dataDashboard'
const DeploymentSite = () => {
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [projectListInfo, setProjectListInfo] = useState({
    inDeployment: 0,
    totalNum: 0,
  })
  const [total, setTotal] = useState(0)
  const [currentPagination, setCurrentPagination] = useState({
    current: 1,
    pageSize: 10,
  })

  const { id, type, industry } = useParams()
  // 查询字段配置
  const queryFields = [
    {
      type: 'select',
      name: 'status',
      label: '部署状态',
      options: [
        { label: '资源下发中', value: '0' },
        { label: '部署中', value: '1' },
        { label: '已部署', value: '2' },
        { label: '洽谈中', value: '3' },
      ],
    },
  ]

  // 表格列配置（带分组表头）
  const columns = [
    {
      title: industry == '0' ? '委办局' : '客户名称',
      dataIndex: industry == '0' ? 'governmentDepartmentName' : 'customerName',
      key: industry == '0' ? 'governmentDepartmentName' : 'customerName',
      fixed: 'left',
    },
    {
      title: '部署状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        return (
          <div
            className=" px-4px py-2px mt-4 text-13 rounded-4px"
            style={{
              backgroundColor: text
                ? PROJECT_STATUS_CONFIG.STATUS_DATA[text].bgColor
                : '#fff',
              width: 'fit-content',
            }}
          >
            {text
              ? queryFields[0].options.find((x) => x.value == text).label
              : '--'}
          </div>
        )
      },
    },
    {
      title: '部署时间',
      dataIndex: 'completedDeploymentTime',
      key: 'completedDeploymentTime',
    },
    {
      title: `7天内活跃情况`,
      dataIndex: 'tenantActivity',
      key: 'tenantActivity',
      children: [
        {
          title: '租户活跃度',
          dataIndex: `sevenDayTenantCount7Active`,
          key: `sevenDayTenantCount7Active`,
          render: (activity, record) => (
            <div>
              {/* 租户活跃度指标 */}
              <div className="flex justify-between items-center">
                <span>
                  {record[`sevenDayTenantCount7Active`]}/{record.tenantSum}
                </span>
                <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
                  <div
                    className="absolute h-6 bg-[#FF5555]"
                    style={{
                      width: `${(record[`sevenDayTenantCount7Active`] / record.tenantSum) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>

              <span className="text-[#999] text-12">
                未活跃：
                {record.tenantSum - record[`sevenDayTenantCount7Active`]}
              </span>
            </div>
          ),
        },
        {
          title: '用户活跃度',
          dataIndex: `sevenDayUserCount7Active`,
          key: `sevenDayUserCount7Active`,
          render: (activity, record) => (
            <div>
              {/* 租户活跃度指标 */}
              <div className="flex justify-between items-center">
                <span>
                  {record[`sevenDayUserCount7Active`]}/{record.userSum}
                </span>
                <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
                  <div
                    className="absolute h-6 bg-[#FF5555]"
                    style={{
                      width: `${(record[`sevenDayUserCount7Active`] / record.userSum) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>

              <span className="text-[#999] text-12">
                未活跃：
                {record.userSum - record[`sevenDayUserCount7Active`]}
              </span>
            </div>
          ),
        },
      ],
    },
    {
      title: `15天内活跃情况`,
      dataIndex: 'tenantActivity',
      key: 'tenantActivity',
      children: [
        {
          title: '租户活跃度',
          dataIndex: `sevenDayTenantCount15Active`,
          key: `sevenDayTenantCount15Active`,
          render: (activity, record) => (
            <div>
              {/* 租户活跃度指标 */}
              <div className="flex justify-between items-center">
                <span>
                  {record[`sevenDayTenantCount15Active`]}/{record.tenantSum}
                </span>
                <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
                  <div
                    className="absolute h-6 bg-[#FF5555]"
                    style={{
                      width: `${(record[`sevenDayTenantCount15Active`] / record.tenantSum) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>

              <span className="text-[#999] text-12">
                未活跃：
                {record.tenantSum - record[`sevenDayTenantCount15Active`]}
              </span>
            </div>
          ),
        },
        {
          title: '用户活跃度',
          dataIndex: `sevenDayUserCount15Active`,
          key: `sevenDayUserCount15Active`,
          render: (activity, record) => (
            <div>
              {/* 租户活跃度指标 */}
              <div className="flex justify-between items-center">
                <span>
                  {record[`sevenDayUserCount15Active`]}/{record.userSum}
                </span>
                <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
                  <div
                    className="absolute h-6 bg-[#FF5555]"
                    style={{
                      width: `${(record[`sevenDayUserCount15Active`] / record.userSum) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>

              <span className="text-[#999] text-12">
                未活跃：
                {record.userSum - record[`sevenDayUserCount15Active`]}
              </span>
            </div>
          ),
        },
      ],
    },
  ]
  if (industry == '0') {
    columns.push(
      {
        title: '硬件信息',
        dataIndex: 'hardwareResource',
        key: 'hardwareResource',
        render: (text) => {
          return '--'
        },
      },

      {
        title: '平台版本号',
        dataIndex: 'applianceVersion',
        key: 'applianceVersion',
        render: (text) => {
          return text ? text : '--'
        },
      },
      {
        title: '渠道来源',
        dataIndex: 'channelName',
        key: 'channelName',
        render: (text) => {
          return text ? text : '--'
        },
      },
      {
        title: '线索等级',
        dataIndex: 'level',
        key: 'level',
        render: (text) => {
          return text ? text : '--'
        },
      },
      {
        title: '商机详细',
        dataIndex: 'detail',
        key: 'detail',
        width: 150,
        render: (text) => {
          return text ? text : '--'
        },
      },
    )
  }

  // 生成模拟数据
  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async (params = {}) => {
    setLoading(true)
    const {
      pageIndex = currentPagination.current,
      pageSize = currentPagination.pageSize,
      status,
    } = params

    try {
      const {
        data: { projectList, projectListInfo, total },
      } = await getProjectList93656({
        type: '4',
        industry,
        pageIndex,
        pageSize,
        regionId: id,
        status, // 传递版本参数给后端
      })
      console.log('projectList', projectList)
      setDataSource(projectList)
      setProjectListInfo(projectListInfo)
      setTotal(total)
      setCurrentPagination({
        current: pageIndex,
        pageSize,
      })
    } catch (error) {
      console.error('获取项目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理搜索
  const handleSearch = (values) => {
    console.log('搜索条件:', values)
    fetchData({
      ...values,
    })
  }

  return (
    <Card>
      {industry == '0' ? (
        <div className="absolute top-22 right-24 text-14">
          <span className="text-#999 text-12">委办局覆盖率：</span>
          <span className="text-#2277FF">{projectListInfo.inDeployment}</span>/
          {projectListInfo.totalNum}
        </div>
      ) : (
        <div className="absolute top-22 right-24 text-14">
          <span className="text-#999 text-12">覆盖数：</span>
          <span className="text-#2277FF">{projectListInfo.inDeployment}</span>个
        </div>
      )}
      <GroupedTable
        columns={columns}
        dataSource={dataSource}
        queryFields={queryFields}
        onSearch={handleSearch}
        pagination={{
          current: currentPagination.current,
          pageSize: currentPagination.pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `总条数: ${total}`,
        }}
        loading={loading}
        tableProps={{
          rowKey: 'key',
        }}
      />
    </Card>
  )
}

// 使用水印高阶组件包装DeploymentSite
export default WithWatermark(DeploymentSite)
