/**
 * 接口名称：获取全国统计首页 - 地图的首页
 * 接口路径：/project/statistics/projectStatistics
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93647
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetProjectStatistics93647 {}
export class IResgetProjectStatistics93647 {
  success?: boolean
  code?: number
  msg?: string
  data?: {
    projectRegionInfo?: object
    projectProvinceList?: object[]
  }
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getProjectStatistics93647: Serve<
  IReqgetProjectStatistics93647,
  IResgetProjectStatistics93647
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetProjectStatistics93647>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/project/statistics/projectStatistics',
      data,
      ...options,
    },
    serviceConfig,
  )
