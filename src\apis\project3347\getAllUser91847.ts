/**
 * 接口名称：获取通讯录人员
 * 接口路径：/baas-sale/clue/getAllUser
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91847
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetAllUser91847 {}
export class IResgetAllUser91847 {
  success?: boolean
  code?: number
  msg?: string
  data?: {
    uid?: number
    name?: string
  }[]
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getAllUser91847: Serve<
  IReqgetAllUser91847,
  IResgetAllUser91847
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetAllUser91847>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/clue/getAllUser',
      data,
      ...options,
    },
    serviceConfig,
  )
