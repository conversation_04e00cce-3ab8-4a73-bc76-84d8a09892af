<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
          name="viewport">
    <meta content="ie=edge" http-equiv="X-UA-Compatible">
    <title></title>
    <!--此文件需要拷贝到 portal-site 打包后的根目录下才能生效-->
    <!--访问路径是 /portal/pc-redirect.html-->
</head>
<body>
<script>
    const search = new URLSearchParams(location.search);
    const target = search.get("target");
    const url = location.origin + '/portal/' + (decodeURIComponent(target) || '');

    if (window.SHMJSBridge) {
      window.SHMJSBridge("system", "openNewTab", "", JSON.stringify({url, "refresh": 1, "title": "内训平台"}));
      window.close()
    } else {
      location.href = url;
    }
</script>
</body>
</html>
