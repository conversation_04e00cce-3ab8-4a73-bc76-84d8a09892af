import { Application, Chat, ConfigProvider, useChat } from '@xm/ai.kit'
import { message, Drawer, Input, Popconfirm, Spin, Button } from 'antd'
import { useEffect, useRef, useState } from 'react'
import icon1 from '../clues/icon.png'
import icon2 from '../demind/icon.png'
// import { getStaticKnowledge91819 } from '@/apis/project3347'
import dayjs from 'dayjs'
import EchartsRender from '@/views/chat/EchartsRender'

const AGENTS = [
  {
    title: '知识库问答',
    code: 3,
    isSuper: true,
    description: '知识库问答',
    welcome: '有什么可以帮你吗？',
    // logo: 'https://saas.uban360.com/statics/config/logo.png',
    // recommend: [
    //   '希望通过 AI 优化现有流程？',
    //   '帮我总结一下这个月的工作内容',
    //   '有哪些具体的AI工具可以提高工作效率？',
    // ],
  },
  {
    title: '线索问题',
    code: 1,
    description: '输入线索相关问题，快速掌握线索情况',
    logo: icon1,
    // recommend: [
    //   '希望通过 AI 优化现有流程？',
    //   '帮我总结一下这个月的工作内容',
    //   '有哪些具体的AI工具可以提高工作效率？',
    // ],
    // schema: [
    //   {
    //     name: '姓名',
    //     component: Input,
    //     placeholder: '请输入姓名',
    //     rules: [{ required: true, message: '请输入姓名' }],
    //   },
    //   {
    //     name: '角色',
    //     component: Select,
    //     options: ['老师', '家长'],
    //     placeholder: '请选择',
    //     rules: [{ required: true, message: '请选择角色' }],
    //   },
    // ],
  },
  {
    title: '需求问答',
    code: 2,
    description: '输入需求相关问题，快速掌握需求情况',
    logo: icon2,
    // schema: [
    //   {
    //     name: '姓名2',
    //     component: Input,
    //     placeholder: '请输入姓名',
    //     rules: [{ required: true, message: '请输入姓名' }],
    //   },
    //   {
    //     name: '角色2',
    //     component: Select,
    //     options: ['老师', '家长'],
    //     placeholder: '请选择',
    //     rules: [{ required: true, message: '请选择角色' }],
    //   },
    // ],
  },
]

export default function App() {
  const applicationRef = useRef(null)
  const dashboardContent = useRef('')
  // const [agentRef, setAgent] = useState(AGENTS[0])
  const agentRef = useRef(AGENTS[0])
  const getHistoryKey = (code) => `localStorage:history:${code}`

  const conversationParamsRef = useRef(null)

  // 获取会话历史记录
  const getLocalConversation = (code, conversationCode) => {
    const history = localStorage.getItem(getHistoryKey(code))
    const list = history ? JSON.parse(history) : []
    if (conversationCode) {
      return list.find((item) => item.id === conversationCode)
    }
    return list.reverse()
  }
  // 创建会话历史记录
  const createLocalConversation = (code, conversationCode, title) => {
    const history = localStorage.getItem(getHistoryKey(code))
    const list = history ? JSON.parse(history) : []
    const item = {
      id: conversationCode,
      title,
      created: dayjs().format('YYYY-MM-DD'),
      historyList: [],
    }
    list.push(item)
    localStorage.setItem(getHistoryKey(code), JSON.stringify(list))
    return item
  }
  // 更新会话历史记录
  const updateLocalConversationHistory = (
    code,
    conversationCode,
    historyList,
  ) => {
    const history = localStorage.getItem(getHistoryKey(code))
    const list = history ? JSON.parse(history) : []
    const item = list.find((item) => item.id === conversationCode)
    if (item) {
      item.historyList = historyList
      localStorage.setItem(getHistoryKey(code), JSON.stringify(list))
    }
  }

  const { chat, loading } = useChat({
    async createConversation() {
      conversationParamsRef.current = {
        appCode: agentRef.current.code,
        conversationCode: String(Date.now()),
      }
      return conversationParamsRef.current
    },
    getBody(params) {
      // console.log('params', params)
      return {
        type: agentRef.current.code,
        userInput: params.content,
      }
    },
    url: '/web-awg/baas-sale/staticKnowledge/stream/getStaticKnowledge',
    onMessage(data) {
      // data 可能是空对象
      if (data.data) {
        const reText = (text = data.data.output) => {
          chat.robot({
            type: 'answer',
            data: { content: text, type: 'text' },
          })
        }
        const reCustom = (chartJsonString) => {
          chat.robot({
            type: 'answer',
            data: { content: chartJsonString, type: 'custom' },
          })
        }

        try {
          if (data.data.output) {
            const json = JSON.parse(data.data.output)
            console.info(json)
            if (json?.type === 'DASHBOARD') {
              // 累加 json 片段
              dashboardContent.current += json.content
            } else if (json?.type === 'MARKDOWN') {
              reText(json.content ?? '')
            } else {
              reText(json.content)
            }
          }
        } catch (e) {
          // console.info('error', e)
          reText()
        }

        if (data.data.end) {
          if (dashboardContent.current) {
            console.info('dashboardContent.current', dashboardContent.current)
            reCustom(dashboardContent.current)
            dashboardContent.current = ''
          }

          chat.robot({
            type: 'actions',
            data: { refresh: true, copy: !dashboardContent.current },
          })
          chat.robot({ type: 'end' })

          setTimeout(() => {
            const { historyList } = chat.getConversationParams()

            // 此处处理创建会话历史记录
            const { conversationCode, appCode } = conversationParamsRef.current
            // console.info('历史会话', appCode, agentRef.current)
            let local = getLocalConversation(
              agentRef.current.code,
              conversationCode,
            )
            if (!local) {
              createLocalConversation(
                agentRef.current.code,
                conversationCode,
                historyList[0].userContent.content,
              )
            }
            updateLocalConversationHistory(
              agentRef.current.code,
              conversationCode,
              historyList,
            )
          }, 300)
        }
      }
      // if (data.messageType === 'CONTENT') {
      //   if (data.reasoningContent) {
      //     chat.robot({ type: 'seek', data: { content: data.reasoningContent } })
      //   } else {
      //     if (data.content) {
      //       chat.robot({ type: 'answer', data: { content: data.content, type: 'text' } })
      //     }
      //   }
      // }
      // if (data.messageType === 'STREAM_END') {
      //   chat.robot({ type: 'actions', data: { refresh: true, copy: true } })
      //   chat.robot({ type: 'end' })
      // }
    },
  })

  const onClick = () => {
    const id = chat.send({ content: '自定义消息', type: 'text' })
    if (id) {
      setTimeout(() => {
        chat.robot({
          type: 'answer',
          data: {
            content:
              '{"answerPartId":362232,"chartSchema":{"chartType":"Column","summary":"销售额柱状图，展示各类别的总销售额","xField":"category","yField":"total_sales"},"databaseInfo":{"dataSourceId":146,"databaseName":"excel_dev","sql":"SELECT COLUMN_8 AS category, SUM(COLUMN_13) AS total_sales FROM 订单_meta_1744114854941 GROUP BY COLUMN_8 ORDER BY total_sales DESC;"},"metaData":{"dataList":[["办公用品","11232.704"],["技术","5857.852000000001"],["家具","2886.1000000000004"]],"headerList":[{"autoIncrement":0,"columnName":"COLUMN_8","columnType":"VARCHAR","dataType":"STRING","name":"category","tableName":"订单_meta_1744114854941"},{"autoIncrement":0,"columnName":"total_sales","columnType":"DOUBLE","dataType":"NUMERIC","name":"total_sales","tableName":""}]}}',
            type: 'custom',
          },
        })
        chat.robot({ type: 'actions', data: { refresh: true, copy: false } })
        chat.robot({ type: 'end' })
      }, 1000)
    }
  }

  useEffect(() => {
    applicationRef.current?.toggleAgent(AGENTS[0])
  }, [])

  return (
    <div
      className={
        'chat bg-white py-16 ' +
        (process.env.PUBLIC_H5 ? 'h-full' : 'h-[calc(100vh-70px-55px-15px)]')
      }
    >
      <div className="max-w-1200 m-auto h-full overflow-auto">
        <ConfigProvider
          message={message}
          Drawer={Drawer}
          Input={Input}
          Spin={Spin}
          Popconfirm={Popconfirm}
        >
          <Application
            mode="ALONE"
            className="h-full"
            ref={applicationRef}
            spin={<Spin spinning />}
            loadMathParams={{ origin: 'https://allinone.uban360.com' }}
            chat={chat}
            onToggleAgent={(_agent) => {
              // setAgent(agentRef)
              agentRef.current = _agent
              console.info(agentRef.current)
              chat.createNew()
              return _agent
            }}
            agents={AGENTS}
          >
            <Chat.History
              onSelect={async (item) => {
                // console.log(item)
                const local = getLocalConversation(
                  agentRef.current.code,
                  item.id,
                )
                const list = local.historyList

                conversationParamsRef.current = {
                  appCode: agentRef.current.code,
                  conversationCode: item.id,
                }

                return {
                  list, // 返回会话内容列表
                  conversation: conversationParamsRef.current,
                }
              }}
              onDelete={async (item) => {
                console.log(item)
                // 此处处理删除会话历史记录
                const appCode = agentRef.current.code
                const list = getLocalConversation(appCode).reverse()
                console.log(list)
                const index = list.findIndex((i) => i.id === item.id)
                console.log(index)
                if (index > -1) {
                  list.splice(index, 1)
                  localStorage.setItem(
                    getHistoryKey(appCode),
                    JSON.stringify(list),
                  )
                  message.success('删除成功')
                }
              }}
              serve={async () => {
                // 获取智能体本地的历史记录
                return getLocalConversation(agentRef.current.code)
              }}
            />
            <Chat.InputBox
              placeholder="请输入您想提问的内容"
              toolbar="CREATE | HISTORY"
              // toolbarRight="AI_AGENTS"
              // toolbarBottom="AI_MODULE | DEEP_SEEK | INTERNET_SEARCH"
              allowStop
              // actions={[
              //   {
              //     key: 'AI_MODULE',
              //     text: '自定义模块名称',
              //   },
              // ]}
            />
            <Chat.RobotResult
              renderCustom={(item) => {
                return (
                  <EchartsRender
                    type="chart"
                    code={item.content}
                    isEnableChat2DB={true}
                  />
                )
              }}
            />
          </Application>
        </ConfigProvider>

        {/*<Button onClick={onClick}>onClick</Button>*/}
      </div>
    </div>
  )
}
