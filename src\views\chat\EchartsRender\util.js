// 将type转换为echarts.series.type
const chartTypeMap = {
  Line: 'line',
  StepLine: 'line',
  AreaLine: 'line',
  Column: 'bar',
  Bar: 'bar',
  Funnel: 'funnel',
  Area: 'area',
  Waterfall: 'waterfall',
  Pie: 'pie',
  WordCloud: 'wordCloud',
  Histogram: 'histogram',
  Table: 'table',
}

export function convertToEChartsConfig(content) {
  const chartType = content.chartSchema.chartType
  let chartConfig = {
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      top: 15, // 留出标题和图例空间
      bottom: 20, // 留出dataZoom空间
      containLabel: true,
      left: 10,
      right: 15,
    },
    legend: {
      top: 0,
      left: 0,
      orient: 'horizontal', // 水平排列
      type: 'scroll', // 启用滚动防止过多条目
    },
  }

  if (chartType === 'Funnel') {
    const dataList = content.metaData.dataList || []

    // 转换为漏斗图格式，并去重
    const funnelData = Object.values(
      dataList.reduce((acc, item) => {
        const name = item[0] // 省份
        const value = parseFloat(item[1]) // 销售额

        if (!acc[name]) {
          acc[name] = { name, value }
        } else {
          acc[name].value += value // 合并同类项
        }
        return acc
      }, {}),
    )

    // 按 value 降序排序
    funnelData.sort((a, b) => b.value - a.value)

    return {
      ...chartConfig,
      series: [
        {
          top: 30,
          bottom: 10,
          width: '90%',
          type: 'funnel',
          data: funnelData,
          minSize: '0%',
          maxSize: '100%',
          left: 'center',
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}: {c}',
          },
        },
      ],
    }
  } else if (
    [
      'Line',
      'StepLine',
      'AreaLine',
      'Column',
      'Bar',
      'Area',
      'Waterfall',
    ].includes(chartType)
  ) {
    // 获取 metaData 中的数据
    const dataList = content.metaData.dataList || []
    const colorField = content.chartSchema.colorField || ''
    const xField = content.chartSchema.xField || ''
    const yField = content.chartSchema.yField || ''

    // 从数据中提取横轴和纵轴的数据
    const xAxisData = dataList.map((item) => item[0]) // 省份
    const yAxisData = dataList.map((item) => item[1]) // 总销售额

    return {
      ...chartConfig,
      color: colorField || '#5B8FF9',
      grid: {
        ...chartConfig.grid,
        top: 20, // 留出标题和图例空间
        bottom: 20, // 留出dataZoom空间
        right: 35,
      },
      xAxis: {
        type: 'category',
        name: xField,
        nameGap: 10,
      },
      yAxis: {
        type: 'value', // 总销售额作为纵轴
        name: yField,
        nameGap: 10,
      },
      series: [
        {
          data: yAxisData, // 总销售额
          type: chartTypeMap[chartType], // 折线图类型（Line、StepLine、AreaLine等）
        },
      ],
    }
  } else if (['Pie', 'RosePie', 'RingPie'].includes(chartType)) {
    // 获取 metaData 中的数据
    const dataList = content.metaData.dataList || []

    // 构建 ECharts 配置
    const pieData = dataList.map((item) => ({
      value: item[1], // 总销售额
      name: item[0], // 省份
    }))

    return {
      ...chartConfig,
      series: [
        {
          type: 'pie',
          data: pieData,
          radius: '50%', // 设置饼图半径
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)', // 标签格式
          },
          ...(chartType === 'RosePie' && {
            roseType: 'radius', // 玫瑰图类型
          }),
          ...(chartType === 'RingPie' && {
            radius: ['40%', '70%'], // 环形图半径
          }),
        },
      ],
    }
  } else if (chartType === 'WordCloud') {
    // 需要 colorField, textField, valueField
    return null
  } else if (chartType === 'Histogram') {
    // 需要 binField, channel, binNumber
    const binField = content.chartSchema.binField || ''
    const channel = content.chartSchema.channel || ''
    const binNumber = content.chartSchema.binNumber || 10 // 默认10个bin
    return {
      ...chartConfig,
      series: [
        {
          type: 'histogram',
          data: binField,
          binNumber: binNumber,
          encode: { x: channel },
        },
      ],
    }
  } else if (chartType === 'Table') {
    const { dataList, headerList } = content.metaData

    // 处理表头
    const columns = headerList.map((header) => ({
      title: header.name,
      dataIndex: header.name,
      key: header.name,
    }))

    // 处理数据，转换为键值对格式
    const tableData = dataList.map((row) => {
      let obj = {}
      headerList.forEach((header, index) => {
        obj[header.name] = row[index]
      })
      return obj
    })

    return {
      type: 'table',
      columns,
      data: tableData,
    }
  }

  return null
}
