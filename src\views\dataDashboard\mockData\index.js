/**
 * 数据看板模拟数据
 * 用于开发和测试阶段
 */

// 导入项目状态配置
import { PROJECT_STATUS_CONFIG } from 'views/dataDashboard/config'

import ChinaMap from 'views/dataDashboard/constants/china.json'
// 模拟状态计数数据 - 基于配置中的状态名称
export const mockStatusCountMap = PROJECT_STATUS_CONFIG.STATUS_DATA.reduce(
  (acc, item) => {
    // 为每个状态生成随机数据，但保持总数一致
    acc[item.name] =
      item.name === '已部署'
        ? 152
        : item.name === '部署中'
          ? 124
          : item.name === '资源下发中'
            ? 21
            : item.name === '洽谈中'
              ? 31
              : 0
    return acc
  },
  {},
)

// 模拟省份项目数量数据
export const mockProvinceProjectCountMap = {
  浙江省: 255,
  广东省: 223,
  上海市: 211,
  北京市: 184,
  重庆市: 153,
  河南省: 156,
  湖南省: 124,
  四川省: 163,
  云南省: 161,
  贵州省: 181,
  陕西省: 171,
  青海省: 145,
  天津市: 141,
  山东省: 131,
  山西省: 89,
  吉林省: 111,
  辽宁省: 98,
  黑龙江省: 86,
  甘肃省: 103,
  河北省: 108,
  江苏省: 104,
  安徽省: 184,
  江西省: 138,
  福建省: 93,
  湖北省: 134,
  内蒙古自治区: 76,
  广西壮族自治区: 65,
  西藏自治区: 46,
  宁夏回族自治区: 38,
  新疆维吾尔自治区: 47,
  香港特别行政区: 83,
  澳门特别行政区: 68,
  台湾省: 158,
}

// 模拟渠道数据 - 基于GPU_CHANNEL常量
export const mockChannelData = [
  { name: '华为', value: 156, percent: '62%' },
  { name: '移动', value: 96, percent: '38%' },
]

// 模拟GPU类型数据 - 基于GPU_BRAND常量
export const mockGpuTypeData = [
  { name: '华为', value: 142, percent: '57%' },
  { name: '英伟达', value: 108, percent: '43%' },
]

// 模拟GPU型号详细数据
export const mockGpuModelData = {
  华为: {
    'Ascend 910': 68,
    'Ascend 310': 42,
    'Atlas 300I': 32,
  },
  英伟达: {
    A100: 45,
    V100: 38,
    T4: 25,
  },
}

// 生成模拟散点数据
export const generateMockScatterData = (count = 100) => {
  // 获取所有状态键
  const statusKeys = PROJECT_STATUS_CONFIG.STATUS_DATA.map(
    (item) => item.statusKey,
  )

  return Array.from({ length: count }, (_, index) => {
    return {
      projectName: `项目${index + 1}`,
      // 随机生成经纬度，覆盖中国主要区域，从views/dataDashboard/constants/china.json中获取
      // 这里简化为随机生成经纬度，范围为中国主要区域
      longLat: [
        ChinaMap.features[Math.floor(Math.random() * ChinaMap.features.length)]
          .geometry.coordinates[0][0][0][0] +
          Math.random() * 0.1 -
          1,
        ChinaMap.features[Math.floor(Math.random() * ChinaMap.features.length)]
          .geometry.coordinates[0][0][0][1] +
          Math.random() * 0.1 -
          0.5,
      ],
      // 从配置的状态键中随机选择一个
      status: statusKeys[Math.floor(Math.random() * statusKeys.length)],
    }
  }).map((el) => {
    return {
      name: el?.projectName,
      value: el?.longLat,
      status: el?.status,
    }
  })
}

// 生成模拟数据的辅助函数
export const generateMockData = () => {
  // 将mockChannelData转换为channelCountMap格式
  const channelCountMap = mockChannelData.reduce((acc, item) => {
    acc[item.name] = item.value
    return acc
  }, {})

  // 将mockGpuModelData转换为gpuTypeMap格式
  const gpuTypeMap = { ...mockGpuModelData }

  // 生成客户类型数据
  const customerTypeCountMap = {
    政府: 125,
    企业: 98,
    学校: 45,
    医院: 32,
    科研单位: 28,
  }

  return {
    totalCount: 328,
    provinceCount: 12,
    statusCountMap: mockStatusCountMap,
    provinceProjectCountMap: mockProvinceProjectCountMap,
    customerTypeCountMap,
    channelCountMap,
    gpuTypeMap,
  }
}
