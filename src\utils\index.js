export function paramToString(obj) {
  return Object.keys(obj)
    .map((k) => {
      return `${k}=${encodeURIComponent(obj[k])}`
    })
    .join('&')
}

export function openPage(path, query = {}) {
  const appid = process.env.PUBLIC_APPID
  console.log(222, 'appid', appid)

  const queryString = paramToString(query)
  window.xm &&
    window.xm.openApp({
      appid,
      relativeUrl: `index.html#${path}${queryString ? `?${queryString}` : ''}`,
      // disableCache: true,
      // isShowHome: false
    })
}

export function session(key, format = Number) {
  const val = sessionStorage.getItem(key)
  return val !== null && val !== undefined ? format(val) : undefined
}
