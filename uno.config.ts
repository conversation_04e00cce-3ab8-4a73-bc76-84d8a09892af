import { defineConfig, presetAttributify, presetUno } from 'unocss'
import transformerVariantGroup from '@unocss/transformer-variant-group'
import transformerDirectives from '@unocss/transformer-directives'
import presetRemToPx from '@unocss/preset-rem-to-px'

export default defineConfig({
  theme: {
    colors: {
      primary: 'var(--xm-primary-color)',
    },
  },
  content: {
    filesystem: ['./src/**/*.{html,js,ts,jsx,tsx}'],
  },
  presets: [
    presetAttributify({
      // antd 的 select 有 size 属性
      ignoreAttributes: ['size'],
    }),
    presetUno(),
    presetRemToPx({ baseFontSize: 4 }),
  ],
  shortcuts: {
    'flex-col': 'flex flex-col',
  },
  variants: [],
  transformers: [transformerVariantGroup(), transformerDirectives()],
})
