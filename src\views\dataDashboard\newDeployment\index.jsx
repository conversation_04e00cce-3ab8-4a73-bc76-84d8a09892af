import React, { useState, useEffect } from 'react'
import { Card, Tag, Tabs } from 'antd'
import { useParams, useHistory } from 'react-router-dom'
import GroupedTable from '@/components/GroupedTable'
import TabFilter from '@/components/TabFilter'
import WithWatermark from '../components/WithWatermark'
import { getProjectList93656, getRegionsList93669 } from '@/apis/dataDashboard'

const InactiveRank = () => {
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [tabItems, setTabItems] = useState([
    {
      key: '7',
      label: '',
    },
    {
      key: '15',
      label: '',
    },
    {
      key: '30',
      label: '',
    },
  ])
  const [activeTab, setActiveTab] = useState('15')
  const [regions, setRegions] = useState([])
  const { id, type } = useParams()
  const queryFields = [
    {
      label: '区域',
      name: 'regionIds',
      type: 'Cascader',
      options: regions,
    },
  ]
  const tabValues = {
    7: 'thirtyDeploy',
    15: 'fifteenDeploy',
    30: 'sevenDeploy',
  }
  // 表格列配置
  const columns = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
      key: 'provinceName',
    },
    {
      title: '市级',
      dataIndex: 'cityName',
      key: 'cityName',
    },
    {
      title: '区县',
      dataIndex: 'countyName',
      key: 'countyName',
    },
    {
      title: '行业',
      dataIndex: 'customerTypeName',
      key: 'customerTypeName',
    },
    {
      title: '已部署时间',
      dataIndex: 'completedDeploymentTime',
      key: 'completedDeploymentTime',
    },
    {
      title: `${activeTab}天内活跃情况`,
      dataIndex: 'tenantActivity',
      key: 'tenantActivity',
      children: [
        {
          title: '租户活跃度',
          dataIndex: `sevenDayTenantCount${activeTab}Active`,
          key: `sevenDayTenantCount${activeTab}Active`,
          render: (activity, record) => (
            <div>
              {/* 租户活跃度指标 */}
              <div className="flex justify-between items-center">
                <span>
                  {record[`sevenDayTenantCount${activeTab}Active`]}/
                  {record.tenantSum}
                </span>
                <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
                  <div
                    className="absolute h-6 bg-[#FF5555]"
                    style={{
                      width: `${(record[`sevenDayTenantCount${activeTab}Active`] / record.tenantSum) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>

              <span className="text-[#999] text-12">
                未活跃：
                {record.tenantSum -
                  record[`sevenDayTenantCount${activeTab}Active`]}
              </span>
            </div>
          ),
        },
        {
          title: '用户活跃度',
          dataIndex: `sevenDayUserCount${activeTab}Active`,
          key: `sevenDayUserCount${activeTab}Active`,
          render: (activity, record) => (
            <div>
              {/* 租户活跃度指标 */}
              <div className="flex justify-between items-center">
                <span>
                  {record[`sevenDayUserCount${activeTab}Active`]}/
                  {record.userSum}
                </span>
                <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
                  <div
                    className="absolute h-6 bg-[#FF5555]"
                    style={{
                      width: `${(record[`sevenDayUserCount${activeTab}Active`] / record.userSum) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>

              <span className="text-[#999] text-12">
                未活跃：
                {record.userSum - record[`sevenDayUserCount${activeTab}Active`]}
              </span>
            </div>
          ),
        },
      ],
    },
    {
      title: '服务器数量',
      dataIndex: 'gpuServerCount',
      key: 'gpuServerCount',
    },
    {
      title: '模型',
      dataIndex: 'modelVersionName',
      key: 'modelVersionName',
    },
  ]
  // 平铺的地区数据组装成树状结构
  const buildRegionTree = (regions, parentId = null) => {
    return regions
      .filter((region) => region.parentId === parentId)
      .map((region) => ({
        ...region,
        value: region.id,
        children: buildRegionTree(regions, region.id),
      }))
  }
  const getRegionsList = async () => {
    try {
      const { data } = await getRegionsList93669()
      console.log('getRegions', data)
      // 平铺的地区数据组装成树状结构
      const treeData = buildRegionTree(data, data[0].parentId)
      console.log('treeData', treeData)
      setRegions(treeData)
    } catch (error) {
      console.error('获取区域列表失败:', error)
    }
  }
  const getProjectList = async (params = {}) => {
    setLoading(true)
    const { pageIndex, pageSize, days, regionId = id } = params

    try {
      const {
        data: { projectList, projectListInfo, total },
      } = await getProjectList93656({
        type: '1',
        days,
        regionId,
        pageIndex,
        pageSize,
      })
      setTabItems((pre) => {
        return pre.map((item) => {
          item.label = `最近${item.key}天(${projectListInfo[tabValues[item.key]]})`
          return item
        })
      })
      console.log('projectList', projectList)
      setDataSource(projectList)
      setPagination({
        current: pageIndex,
        pageSize,
        total,
      })
    } catch (error) {
      console.error('获取项目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    getRegionsList()
  }, [])
  // 根据选项卡选择获取数据
  useEffect(() => {
    getProjectList({ days: activeTab, pageIndex: 1 })
  }, [activeTab, id])

  // 处理搜索
  const handleSearch = (values) => {
    console.log('搜索条件:', values)
    const { regionIds, current, pageSize } = values
    const specialCodes = [110000, 120000, 310000, 500000]

    // 判断是否为分页操作：如果包含 current 或 pageSize 参数，则认为是分页操作
    const isPagination = current !== undefined || pageSize !== undefined

    if (regionIds && !isPagination) {
      // 区域选择操作：导航到新的URL，regionId会通过useEffect更新
      history.push(
        `/dataDashboard/newDeployment/${regionIds.length == 3 || (specialCodes.includes(regionIds[0]) && regionIds.length == 2) ? '2' : '1'}/${regionIds[regionIds.length - 1]}`,
      )
    } else {
      // 分页操作或其他搜索操作：直接查询数据
      getProjectList({
        ...values,
        pageIndex: values.current || pagination.current,
        days: activeTab,
        regionId:
          regionIds && regionIds.length > 0
            ? regionIds[regionIds.length - 1]
            : id,
      })
    }
  }

  return (
    <Card className="relative">
      <TabFilter
        className="absolute top-16 left-16"
        items={tabItems}
        activeKey={activeTab}
        onChange={setActiveTab}
      />
      <GroupedTable
        columns={columns}
        dataSource={dataSource}
        queryFields={queryFields}
        onSearch={handleSearch}
        pagination={pagination}
        loading={loading}
        fieldAlign="end"
        tableProps={{
          rowKey: 'key',
        }}
      />
    </Card>
  )
}

// 使用水印高阶组件包装InactiveRank
export default WithWatermark(InactiveRank)
