/**
 * 接口名称：获取线索或需求ID
 * 接口路径：/baas-sale/comment/getCommentTypeId
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91866
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetCommentTypeId91866 {
  /**
   * 评论ID
   */
  id: string | number
}
export class IResgetCommentTypeId91866 {
  code?: number
  msg?: string
  /**
   * 线索或需求ID
   */
  data?: number
  success?: boolean
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getCommentTypeId91866: Serve<
  IReqgetCommentTypeId91866,
  IResgetCommentTypeId91866
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetCommentTypeId91866>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/comment/getCommentTypeId',
      data,
      ...options,
    },
    serviceConfig,
  )
