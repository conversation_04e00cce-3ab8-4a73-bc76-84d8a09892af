/**
 * 接口名称：省(市列表) 市(区列表) 统计
 * 接口路径：/project/statistics/region
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93653
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetProjectRegionList93653 {}
export class IResgetProjectRegionList93653 {
  success?: boolean
  code?: number
  msg?: string
  data?: object
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getProjectRegionList93653: Serve<
  IReqgetProjectRegionList93653,
  IResgetProjectRegionList93653
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetProjectRegionList93653>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/project/statistics/region',
      data,
      ...options,
    },
    serviceConfig,
  )
