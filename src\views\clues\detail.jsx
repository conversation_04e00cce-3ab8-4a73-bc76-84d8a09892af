import {
  Card,
  Col,
  Descriptions,
  Divider,
  Row,
  Skeleton,
  Space,
  Typography,
} from 'antd'
import { useHistory } from 'react-router-dom'
import XmIconReact from '@xm/icons-ai/react-svg'

import ICON_SUMMARY from '@/assets/img/icon-summary.png'
import Comments from '@/components/Comments'
import { useRequest } from 'ahooks'
import { getClue91846, getCommentTypeId91866 } from '@/apis/project3347'
import { getLabelByValue } from '@/utils'
import dayjs from 'dayjs'

import {
  budgetStatus,
  clueLevel,
  industryOptions,
  projectPhase,
  projectStatus,
} from '@/views/clues/constants'
import { renderProjectStatus } from '@/views/clues/index'
import { useState } from 'react'

const { Title, Paragraph, Text } = Typography

const CustomerDetail = () => {
  const history = useHistory()
  const searchParams = new URLSearchParams(history.location.search)
  const [id, setId] = useState(searchParams.get('id'))
  const commentId = searchParams.get('commentId')
  const {
    data: detail = {},
    loading,
    run,
  } = useRequest(
    async () => {
      let _id = id
      if (!id && commentId) {
        const { data } = await getCommentTypeId91866(
          {
            id: commentId,
          },
          {
            hideMessage: true,
          },
        )

        _id = data
        setId(_id)
      }
      const { data } = await getClue91846({
        id: _id,
      })

      return data
    },
    {
      onError: (e) => {
        if (e.code === 500) {
          history.replace('/error/404')
        }
      },
    },
  )

  return (
    <>
      <Skeleton active loading={loading}>
        <div className="bg-white py-20 rounded-4 px-24">
          <div className="flex-col  mb-16 md:flex-row md:justify-between md:items-start">
            <div className="flex-col gap-8">
              {renderProjectStatus(detail.projectStatus)}
              <Title level={4} className="!mb-0 flex-1">
                {detail.aiTitle || '等待模型生成中'}
              </Title>
            </div>

            <Space className="mt-8 md:mt-0">
              <span
                className="flex items-center md:w-full mr-16 cursor-pointer text-primary"
                onClick={() => {
                  history.push(`/demind/create?clue=${id}`)
                }}
              >
                <XmIconReact
                  className="mr-4"
                  color="var(--xm-primary-color)"
                  name="document_added_line"
                />
                需求上报
              </span>
              {!process.env.PUBLIC_H5 ? (
                <span
                  className="flex items-center cursor-pointer text-primary"
                  onClick={() => {
                    history.push(`/clues/create/?id=${id}`)
                  }}
                >
                  <XmIconReact
                    className="mr-4"
                    color="var(--xm-primary-color)"
                    name="bianji_line"
                  />
                  编辑
                </span>
              ) : null}
            </Space>
          </div>

          <div
            className="mb-32 border-1 border-solid border-[rgba(0,0,0,0)] rounded-8 bg-[linear-gradient(to_right,#fff,#fff),linear-gradient(90deg,rgba(48,135,255,0.3),rgba(243,137,255,0.3))]"
            style={{
              backgroundClip: 'padding-box, border-box',
              backgroundOrigin: 'padding-box, border-box',
            }}
          >
            <div className="flex-col text-#5C626B text-14 bg-[linear-gradient(96deg,#3087ff0f_0%,#f389ff0f_100%)] p-16 rounded-8">
              <img className="w-[70px] h-22 mb-8" src={ICON_SUMMARY} alt="" />
              {detail.aiSummarize || '等待模型生成中'}
            </div>
          </div>

          <Card
            title="基本信息"
            className="!shadow-none"
            bordered={false}
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
          >
            <Descriptions column={3} layout="vertical">
              <Descriptions.Item label="客户名称">
                {detail.customerCompany || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="所在区域">
                {detail.regionName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="行业">
                {getLabelByValue(detail.industry, industryOptions)}
              </Descriptions.Item>
              <Descriptions.Item label="项目金额">
                {detail.projectAmount || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="预算到位情况">
                {getLabelByValue(detail.budgetPlace, budgetStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="资金来源">
                {detail.fundSource || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="客户是否立项">
                {getLabelByValue(detail.projectApproval, projectStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="预计签单时间">
                {detail.expectSigningDate
                  ? dayjs(detail.expectSigningDate).format('YYYY-MM-DD')
                  : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Divider className="my-16" />
          <Card
            title="服务信息"
            className="!shadow-none"
            bordered={false}
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
          >
            <Descriptions column={1} layout="vertical">
              <Descriptions.Item label="线索等级" span={12}>
                {getLabelByValue(detail.clueLevel, clueLevel) || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="项目阶段" span={12}>
                {getLabelByValue(detail.projectStatus, projectPhase) || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="项目背景">
                <pre className="break-words leading-[1.5] whitespace-pre-line">
                  {detail.projectBackground || '-'}
                </pre>
              </Descriptions.Item>
              <Descriptions.Item label="当前进展">
                <pre className="break-words leading-[1.5] whitespace-pre-line">
                  {detail.progress || '-'}
                </pre>
              </Descriptions.Item>
              <Descriptions.Item label="具体需求">
                <pre className="break-words leading-[1.5] whitespace-pre-line">
                  {detail.demandContent || '-'}
                </pre>
              </Descriptions.Item>
              <Descriptions.Item label="客户痛点">
                <pre className="break-words leading-[1.5] whitespace-pre-line">
                  {detail.customerPain || '-'}
                </pre>
              </Descriptions.Item>
              <Descriptions.Item label="需求提出者身份及角色">
                {detail.demandProposeStaff || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="客户决策链">
                {detail.demandProposeDecision || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="讯盟报价及方案">
                <pre className="break-words leading-[1.5] whitespace-pre-line">
                  {detail.customerDecisionMaking || '-'}
                </pre>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Divider className="my-16" />
          <Card
            title="生态合作"
            className="!shadow-none"
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
            bordered={false}
          >
            <Descriptions column={3} layout="vertical">
              <Descriptions.Item label="厂家名称">
                {detail.manufacturerName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="合作模式">
                {detail.cooperateMode || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="合作需求">
                {detail.cooperateDemand || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="当前进展">
                {detail.cooperateProgress || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Divider className="my-16" />
          <Card
            title="竞品信息"
            className="!shadow-none"
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
            bordered={false}
          >
            <pre className="text-#262A30 text-14 break-words leading-[1.5] whitespace-pre-line">
              {detail.competitors || '-'}
            </pre>
          </Card>
          <Divider className="my-16" />
          <Card
            title="项目相关人员"
            className="!shadow-none"
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
            bordered={false}
          >
            <Title className="text-14" level={5}>
              讯盟对接人
            </Title>
            <Descriptions column={3} layout="vertical">
              <Descriptions.Item label="销售">
                {detail.saleDockingName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.saleDockingPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="" />
              <Descriptions.Item label="售前">
                {detail.preDockingName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.preDockingPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="" />
              <Descriptions.Item label="产品">
                {detail.projectDockingName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.projectDockingPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="" />
              <Descriptions.Item label="生态">
                {detail.ecologyDockingName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.ecologyDockingPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="" />
              <Descriptions.Item label="其他">
                {detail.otherDockingName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.otherDockingPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="" />
            </Descriptions>
            <Title className="text-14 mt-8" level={5}>
              生态对接人
            </Title>
            <Descriptions column={3} layout="vertical">
              <Descriptions.Item label="生态对接人">
                {detail.ecologyName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.ecologyPhone || '-'}
              </Descriptions.Item>
            </Descriptions>
            <Title className="text-14 mt-8" level={5}>
              合作方对接人
            </Title>
            <Descriptions column={3} layout="vertical">
              <Descriptions.Item label="合作方对接人">
                {detail.manufacturerName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {detail.cooperateMode || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Divider className="my-16" />
          <Card
            title="总要求"
            className="!shadow-none"
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
            bordered={false}
          >
            <Descriptions column={1} layout="vertical">
              <Descriptions.Item label="现有的系统情况，有哪些需求没能满足">
                <pre className="text-#262A30 text-14 break-words leading-[1.5] whitespace-pre-line">
                  {detail.notSatisfied || '-'}
                </pre>
              </Descriptions.Item>
              <Descriptions.Item label="需求实现的难点在于哪些">
                <pre className="text-#262A30 text-14 break-words leading-[1.5] whitespace-pre-line">
                  {detail.demandDiff || '-'}
                </pre>
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Divider className="my-16" />
          <Card
            title="备注信息"
            className="!shadow-none"
            styles={{
              header: {
                paddingInline: 0,
                paddingBottom: '12px',
                borderBottom: 0,
                minHeight: 'auto',
              },
              body: {
                padding: 0,
              },
            }}
            bordered={false}
          >
            <pre className="text-#262A30 text-14 break-words leading-[1.5] whitespace-pre-line">
              {detail.description || '-'}
            </pre>
          </Card>
        </div>

        <Card
          title="关联需求"
          className="!shadow-none my-16"
          styles={{
            header: {
              borderBottom: 0,
            },
            body: {
              paddingTop: 0,
            },
          }}
          bordered={false}
        >
          {detail?.demandList?.length ? (
            <Row gutter={20} className="gap-16 md:gap-0">
              {detail?.demandList?.map((el) => {
                return (
                  <Col
                    key={el.id}
                    span={24}
                    md={{
                      span: 8,
                    }}
                  >
                    <div
                      onClick={() => {
                        history.push(`/demind/detail/?id=${el.id}`)
                      }}
                      className="flex items-center bg-#F6F8FD rounded-4 px-12 py-9 text-#262A30 text-14 hover:text-[var(--xm-primary-color)] [&:hover_path]:fill-[var(--xm-primary-color)] cursor-pointer"
                    >
                      <XmIconReact
                        name="document_editor_line"
                        className="mr-10 flex items-center"
                      />
                      <span className="line-clamp-1">{el.title}</span>
                    </div>
                  </Col>
                )
              })}
            </Row>
          ) : (
            '-'
          )}
        </Card>

        <Comments
          onRefresh={async () => {
            const { data } = await getClue91846({
              id,
            })
            return data.commentList
          }}
          id={id}
          type={0}
          comments={detail?.commentList}
        />
      </Skeleton>
    </>
  )
}

export default CustomerDetail
