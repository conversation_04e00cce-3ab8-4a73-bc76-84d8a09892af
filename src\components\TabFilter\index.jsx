import React from 'react'

/**
 * TabFilter - 一个可重用的选项卡过滤器组件
 *
 * 使用示例:
 * ```jsx
 * const [activeTab, setActiveTab] = useState('tab1');
 * const tabItems = [
 *   { key: 'tab1', label: '选项一' },
 *   { key: 'tab2', label: '选项二' },
 *   { key: 'tab3', label: '选项三' },
 * ];
 *
 * <TabFilter
 *   items={tabItems}
 *   activeKey={activeTab}
 *   onChange={setActiveTab}
 * />
 * ```
 *
 * @param {Object} props
 * @param {Array} props.items - 带有key和label属性的选项卡项目数组
 * @param {string} props.activeKey - 当前激活的选项卡键
 * @param {Function} props.onChange - 选项卡变更时的回调函数
 * @param {string} props.className - 附加的类名
 * @param {Object} props.style - 附加的样式
 * @param {string} props.containerClassName - 容器的类名
 * @param {string} props.itemClassName - 每个选项卡项目的类名
 * @param {string} props.activeItemClassName - 激活选项卡项目的类名
 * @returns {JSX.Element}
 */
const TabFilter = ({
  items = [],
  activeKey,
  onChange,
  className = '',
  style = {},
  containerClassName = 'inline-flex gap-24 bg-[#F7F8F9] px-16 py-5 mb-16 w-auto rounded-4',
  itemClassName = 'px-10 py-2 cursor-pointer',
  activeItemClassName = 'text-#2277FF font-600 bg-#fff rounded-4',
}) => {
  return (
    <div
      className={`tab-filter-container ${containerClassName} ${className}`}
      style={style}
    >
      {items.map((item) => (
        <div
          key={item.key}
          className={`${itemClassName} ${activeKey === item.key ? activeItemClassName : ''}`}
          onClick={() => onChange(item.key)}
        >
          {item.label}
        </div>
      ))}
    </div>
  )
}

export default TabFilter
