{"name": "business-sale", "private": true, "version": "1.0.0", "scripts": {"dev": "rsbuild dev", "dev:h5": "rsbuild dev --env-mode h5", "build": "rsbuild build && node after-build.js", "build:h5": "rsbuild build --env-mode h5 && node after-build.js", "format": "biome format --write", "preview": "rsbuild preview", "sync:config": "curl -sSL 'http://**********:7002/gitFileContent?repo=remote-config&path=ai-edu/install.js' | node - local.config.ts", "prepare": "npx simple-git-hooks && node install.js && pnpm sync:config", "yapi": "yapiMagic"}, "simple-git-hooks": {"pre-commit": "npx lint-staged --concurrent=false -d"}, "lint-staged": {"src/**/*.{ts,tsx}": ["node ./tsc-files --noEmit --pretty"], "*.{js,jsx,ts,tsx}": ["npm run format"]}, "dependencies": {"@ant-design/cssinjs": "^1.21.1", "@ant-design/icons": "^5.4.0", "@ant-design/pro-components": "^2.8.6", "@emotion/css": "^11.13.5", "@unocss/reset": "^0.61.6", "@xm/ai.kit": "^0.0.53", "@xm/ai.kit.base": "^0.0.52", "@xm/business-util": "^0.0.31", "@xm/icons-ai": "^0.0.237", "@xm/native": "^1.0.50", "@xm/xnative": "^0.0.7", "@xm/xpro-react": "^1.1.34", "@xm/xutil": "^2.0.4", "ahooks": "^3.8.0", "antd": "^5.19.3", "antd-mobile": "^5.39.0", "axios": "^1.7.2", "dayjs": "^1.11.12", "downloadjs": "^1.4.7", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "hox": "^2.1.1", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "react": "^17.0.2", "react-dom": "^17.0.2", "react-draggable": "^4.4.6", "react-resizable": "^3.0.5", "react-router-dom": "5"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@module-federation/enhanced": "^0.3.3", "@rsbuild/core": "1.2.19", "@rsbuild/plugin-basic-ssl": "^1.1.1", "@rsbuild/plugin-less": "^1.1.1", "@rsbuild/plugin-react": "^1.1.1", "@types/lodash-es": "^4.17.12", "@types/node": "16.0.0", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-router-dom": "^5.3.3", "@unocss/postcss": "66.1.0-beta.5", "@unocss/preset-rem-to-px": "66.1.0-beta.5", "@unocss/preset-uno": "66.1.0-beta.5", "@unocss/transformer-directives": "66.1.0-beta.5", "@unocss/transformer-variant-group": "66.1.0-beta.5", "@xm/icons-ai-education": "0.0.46", "@xm/xapp-main": "^0.2.2", "@xm/yapi-to-code": "^0.5.1", "adm-zip": "^0.5.16", "lint-staged": "^15.2.7", "postcss-preset-env": "^10.0.3", "simple-git-hooks": "^2.11.1", "tsc-files": "^1.1.4", "typescript": "^5.5.2", "unocss": "66.1.0-beta.7"}, "pnpm": {"packageExtensions": {"@ant-design/pro-utils": {"dependencies": {"rc-field-form": "^2.2.1"}}}}}