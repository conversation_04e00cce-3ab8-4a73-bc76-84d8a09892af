import { defineConfig } from './local.config'
// import { OutputConfig } from '@rsbuild/core'
// import { ModuleFederationPlugin } from '@module-federation/enhanced/rspack'

export default defineConfig((config) => {
  const assetPrefix =
    config.env === 'production' && !process.env.PUBLIC_H5
      ? '/web/new-web/business-sale'
      : './'

  return {
    host: 'localhost',
    source:
      config.envMode === 'h5'
        ? {
            // entry: {
            //   index: './src/index.h5.jsx',
            // },
            define: {
              'process.env.PUBLIC_H5': JSON.stringify(process.env.PUBLIC_H5),
              'process.env.PUBLIC_APPID': JSON.stringify(
                process.env.PUBLIC_APPID,
              ),
            },
          }
        : undefined,
    appName: 'business-sale',
    port: 3001,
    html: {
      template: './static/dev.html',
    },
    modifyRsbuildConfig(config) {
      // console.log(config)
    },
    output: {
      // polyfill: 'usage',
      assetPrefix,
      // externals: (({ context, request }, callback) => {
      //   // if (/^antd/.test(request)) {
      //   //   console.log(context)
      //   // }
      //   if (context.includes('views/index')) {
      //     console.log(request)
      //   }
      //   callback()
      // }) as OutputConfig['externals']
    },
    dev: {
      // proxy: {
      //   '/web-awg': {
      //     target: 'https://saas-dev.uban360.com',
      //     changeOrigin: true,
      //     secure: false,
      //     // pathRewrite: {
      //     //   '^/api': '',
      //     // },
      //   },
      // },
      setupMiddlewares: [
        // (middlewares, devServer) => {
        //   const indexDeps: Array<string | undefined> = []
        //
        //   // 取出 html 中的依赖引用
        //   devServer.environments.web.getTransformedHtml('index').then((res) => {
        //     const match = res.match(/src="(.*?)"/g)
        //     if (match) {
        //       const i = match.findIndex((x) => x.includes('index.js'))
        //       const deps = match
        //         .slice(0, i + 1)
        //         .map((x) => x.match(/src="(.*?)"/)?.[1])
        //         .map((x) => x?.replace('localhost', '0.0.0.0'))
        //       Array.prototype.push.apply(indexDeps, deps)
        //     }
        //   })
        //
        //   // 改写入口文件请求
        //   // 修改及其依赖的响应行为
        //   middlewares.unshift((req, res, next) => {
        //     if (req.url?.includes('/static/js/index.js?t=')) {
        //       const response = indexDeps
        //         .map((x, i) =>
        //           `
        //       const script${i} = document.createElement('script');
        //       script${i}.src = '${x}';
        //       document.body.appendChild(script${i});
        //       `.trim(),
        //         )
        //         .join('\n')
        //       res.setHeader('Content-Type', 'text/javascript')
        //       res.end(response)
        //     } else {
        //       next()
        //     }
        //   })
        //
        //   return middlewares
        // },
      ],
    },
    tools: {
      rspack: {
        plugins: [
          // new ModuleFederationPlugin({
          //   dts: false,
          //   name: 'answerSheet',
          //   filename: 'remoteEntry.js',
          //   exposes: {
          //     './helper': './src/components/Editor/store/helper.ts',
          //     './contants': './src/components/Editor/contants.ts',
          //     './utils': './src/utils/index.ts',
          //   },
          //   shared: {
          //     // react: {
          //     //   singleton: true,
          //     //   requiredVersion: '18.2.0',
          //     //   eager: true,
          //     // },
          //     // 'react-dom': {
          //     //   singleton: true,
          //     //   requiredVersion: '18.2.0',
          //     //   eager: true,
          //     // },
          //     // 'react-router': {
          //     //   singleton: true,
          //     //   eager: true,
          //     // },
          //     // 'react-router-dom': {
          //     //   singleton: true,
          //     //   eager: true,
          //     // },
          //     // antd: {
          //     //   singleton: true,
          //     //   eager: true,
          //     // },
          //     // dayjs: { eager: true },
          //     moment: { eager: true },
          //   },
          //   runtimePlugins: [
          //     // 可以用来动态处理内容
          //     // './scripts/runtime-plugin.ts'
          //   ],
          //   getPublicPath: `return '${assetPrefix}/'`,
          // }),
        ],
      },
    },
  }
})
