/**
 * 接口名称：获取线索列表
 * 接口路径：/baas-sale/clue/getClueList
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91842
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetClueList91842 {
  /**
   * 项目状态，0:初步建联，1：POC阶段，2：商务阶段，3：已签单，4：搁置，5：关闭
   */
  projectStatus?: number
  /**
   * 部门ID
   */
  deptId?: number
  /**
   * 分页
   */
  pageIndex?: number
  /**
   * 条数
   */
  pageSize?: number
}
export class IResgetClueList91842 {
  success?: boolean
  code?: number
  msg?: string
  data?: {
    total?: number
    list?: {
      /**
       * 线索ID
       */
      id?: number
      /**
       * 区域ID
       */
      regionId?: string
      /**
       * 区域名称
       */
      regionName?: string
      /**
       * 客户单位名称
       */
      customerCompany?: string
      /**
       * 销售对接人
       */
      saleDockingName?: string
      /**
       * 售前对接人
       */
      preDockingName?: string
      /**
       * 产品对接人
       */
      projectDockingName?: string
      /**
       * 项目状态，0:初步建联，1：POC阶段，2：商务阶段，3：已签单，4：搁置，5：关闭
       */
      projectStatus?: number
    }[]
  }
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getClueList91842: Serve<
  IReqgetClueList91842,
  IResgetClueList91842
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetClueList91842>(
    {
      method: 'post',
      url: '/web-awg/baas-sale/clue/getClueList',
      data,
      ...options,
    },
    serviceConfig,
  )
