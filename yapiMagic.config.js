/* eslint-disable */
const { getConfig } = require('@xm/xutil/dist/yapi/config')

module.exports = getConfig(
  [
    {
      url: 'http://yapi.shinemo.com',
      id: 3347,
      dir: 'src/apis',
    },
  ],
  'ts',
  null,
  /*
   * formatUrlPrefix
   * 根据自己的要求去自定义拼接接口path前缀
   * @params api {object} yapi 脚手架内部属性，高阶定制可用
   * @params serviceName {string} yapi 文档 headers 中后端填充的服务名称
   * @params common {boolean} yapi 文档 tag 中后端自动选择的标签，表示该接口是多端公用
   */
  (props) => {
    const { api, serviceName } = props
    const prefix = [serviceName ? '/' + serviceName : '/web-awg']
      .filter(Boolean)
      .join('/')
    return `${prefix}${api.basepath}${api.path}`
  },
  ({ api, urlPath, requestFromPackageName, serverUrl, projectId }) => {
    const arr = [
      `
    /**
    * 接口名称：${api.title}
    * 接口路径：${api.path || ''}
    * 文档地址：${serverUrl}/project/${projectId}/interface/api/${api.id}
    **/
      `,
      '',
    ]
    arr.push(
      `import { request, HttpOptions } from '${requestFromPackageName}'`,
      "import { AxiosRequestConfig } from 'axios'",
      api.requestInterface,
      api.responseInterface,
    )
    arr.push(
      'type Serve<T, G> = (data?: T, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) => Promise<G>',
      `export const ${api.name}: Serve<${api.reqInterfaceName}, ${api.resInterfaceName}> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) => request<${api.resInterfaceName}>({
      method: '${api.method.toLocaleLowerCase()}',
      url: '${urlPath}',
      data,
      ...options
    }, serviceConfig) `,
    )

    return arr.join(`
        `)
  },
  '..',
)
