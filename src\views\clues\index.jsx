import { ProTable } from '@ant-design/pro-components'

// const Tree = WEB_UTIL.Tree
import { useRef } from 'react'
import { Badge, Button, Space } from 'antd'
import { useHistory } from 'react-router-dom'
import { projectPhase } from '@/views/clues/constants'
import { getClueList91842 } from '@/apis/project3347'
import { useRoleStore } from '@/context/useRoleStore'
import UserSelector from '@/components/UserSelector'

export const renderProjectStatus = (value) => {
  const status = projectPhase.find((item) => item.value === +value)
  return <Badge color={status?.color} text={status?.label || '-'} />
}

const Index = () => {
  const history = useHistory()
  const tableRef = useRef()
  const formRef = useRef()
  const { hasDeptSelect } = useRoleStore()

  // 表格列定义
  const columns = [
    hasDeptSelect && {
      title: '部门',
      dataIndex: 'deptId',
      key: 'deptId',
      hideInTable: true,
      renderFormItem: (_, props) => {
        const value = formRef.current?.getFieldValue('deptId')
        return (
          <UserSelector
            title="选择部门"
            allowClear
            onChange={(val) => {
              formRef.current.setFieldsValue({ deptId: val })
              formRef.current.submit()
            }}
            value={value}
            treeProps={{
              orgTree: {
                add: true,
                isopen: true,
                chooseDept: true,
                onlyDept: true,
              },
            }}
          />
        )
      },
    },
    {
      title: '地区',
      dataIndex: 'regionName',
      key: 'regionName',
      search: false,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'customerCompany',
      key: 'customerCompany',
      search: false,
      ellipsis: true,
    },
    {
      title: '销售',
      dataIndex: 'saleDockingName',
      key: 'saleDockingName',
      search: false,
      ellipsis: true,
    },
    {
      title: '售前',
      dataIndex: 'preDockingName',
      key: 'preDockingName',
      search: false,
      ellipsis: true,
    },
    {
      title: '产品',
      dataIndex: 'projectDockingName',
      key: 'projectDockingName',
      search: false,
      ellipsis: true,
    },
    {
      title: '商机状态',
      dataIndex: 'projectStatus',
      search: true,
      key: 'projectStatus',
      valueType: 'select',
      width: 150,
      fieldProps: {
        onChange() {
          formRef.current.submit()
        },
        options: projectPhase,
      },
      render: (_, record) => {
        return renderProjectStatus(record.projectStatus)
      },
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      width: 220,
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              history.push(`./clues/detail?id=${record.id}`)
            }}
          >
            详情
          </Button>
          <Button
            size="small"
            type="link"
            onClick={() => {
              history.push(`./demind/create?clue=${record.id}`)
            }}
          >
            上报需求
          </Button>
          <Button
            size="small"
            type="link"
            onClick={() => {
              history.push(`./clues/create?id=${record.id}`)
            }}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ].filter(Boolean)

  return (
    <div className="bg-white p-16 rounded-md">
      <ProTable
        formRef={formRef}
        actionRef={tableRef}
        request={async ({ current, pageSize, deptId, ...rest }) => {
          const { data } = await getClueList91842({
            pageIndex: current,
            pageSize,
            deptId: hasDeptSelect ? deptId?.id : undefined,
            ...rest,
          })
          return { data: data?.list || [], success: true, total: data?.total }
        }}
        expandable={false}
        pagination={{
          showTotal: (t) => `共${t}条`,
          showSizeChanger: true,
          // showQuickJumper: true,
          defaultPageSize: 10,
        }}
        columns={columns}
        rowKey="id"
        search={{
          className: '[&_.business-ant-pro-query-filter-actions]:hidden',
          collapsed: false,
          collapseRender: false,
          searchGutter: 16,
          resetText: null,
          span: 8,
          optionRender: (_, formProps, dom) => {
            return []
          },
        }}
        toolBarRender={false}
      />
    </div>
  )
}

export default Index
