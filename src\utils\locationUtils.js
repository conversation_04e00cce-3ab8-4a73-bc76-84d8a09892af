/**
 * 地区数据工具函数
 * 提供省市区层级数据和根据code获取完整地区名称的功能
 */
import REGION_LIST from '@/assets/city-options/list.json'

/**
 * 省市区层级数据
 * 按照省市区三级结构组织
 * 每个节点包含 code 和 name
 */
export const LOCATION_DATA = (() => {
  const provinces = {}
  const cities = {}
  const districts = {}

  // 遍历所有地区数据，按照编码规则分类
  Object.keys(REGION_LIST).forEach((code) => {
    const name = REGION_LIST[code]

    // 省级: 编码以 0000 结尾
    if (code.endsWith('0000')) {
      provinces[code] = {
        code,
        name,
        cities: {},
      }
    }
    // 市级: 编码以 00 结尾但不以 0000 结尾
    else if (code.endsWith('00') && !code.endsWith('0000')) {
      cities[code] = {
        code,
        name,
        districts: {},
      }

      // 关联到对应的省
      const provinceCode = code.substring(0, 2) + '0000'
      if (provinces[provinceCode]) {
        provinces[provinceCode].cities[code] = cities[code]
      }
    }
    // 区县级: 不以 00 结尾的编码
    else if (!code.endsWith('00')) {
      districts[code] = {
        code,
        name,
      }

      // 关联到对应的市
      const cityCode = code.substring(0, 4) + '00'
      if (cities[cityCode]) {
        cities[cityCode].districts[code] = districts[code]
      }
    }
  })

  return provinces
})()

/**
 * 根据地区编码获取完整的地区名称
 * @param {string} code - 地区编码
 * @param {string} separator - 分隔符，默认为空格
 * @returns {string} 完整的地区名称，例如：浙江省 杭州市 西湖区
 */
export function getFullLocationName(code, separator = ' ') {
  if (!code || !REGION_LIST[code]) {
    return ''
  }

  // 如果是省级编码
  if (code.endsWith('0000')) {
    return REGION_LIST[code]
  }

  // 如果是市级编码
  if (code.endsWith('00')) {
    const provinceCode = code.substring(0, 2) + '0000'
    const provinceName = REGION_LIST[provinceCode] || ''
    const cityName = REGION_LIST[code] || ''

    return provinceName && cityName
      ? `${provinceName}${separator}${cityName}`
      : cityName || provinceName
  }

  // 如果是区县级编码
  const provinceCode = code.substring(0, 2) + '0000'
  const cityCode = code.substring(0, 4) + '00'

  const provinceName = REGION_LIST[provinceCode] || ''
  const cityName = REGION_LIST[cityCode] || ''
  const districtName = REGION_LIST[code] || ''

  return [provinceName, cityName, districtName].filter(Boolean).join(separator)
}

/**
 * 根据地区编码获取地区信息对象
 * @param {string} code - 地区编码
 * @returns {Object|null} 地区信息对象，包含省市区信息
 */
export function getLocationInfo(code) {
  if (!code || !REGION_LIST[code]) {
    return null
  }

  const provinceCode = code.substring(0, 2) + '0000'
  const cityCode = code.substring(0, 4) + '00'

  const result = {
    province: {
      code: provinceCode,
      name: REGION_LIST[provinceCode] || '',
    },
    city: {
      code: cityCode,
      name: REGION_LIST[cityCode] || '',
    },
    district: {
      code,
      name: REGION_LIST[code] || '',
    },
  }

  // 如果是省级编码，清空市和区信息
  if (code.endsWith('0000')) {
    result.city = { code: '', name: '' }
    result.district = { code: '', name: '' }
  }

  // 如果是市级编码，清空区信息
  if (code.endsWith('00') && !code.endsWith('0000')) {
    result.district = { code: '', name: '' }
  }

  return result
}

/**
 * 获取所有省份列表
 * @returns {Array} 省份列表，每项包含code和name
 */
export function getAllProvinces() {
  return Object.keys(LOCATION_DATA).map((code) => ({
    code,
    name: LOCATION_DATA[code].name,
  }))
}

/**
 * 根据省份编码获取所有城市列表
 * @param {string} provinceCode - 省份编码
 * @returns {Array} 城市列表，每项包含code和name
 */
export function getCitiesByProvince(provinceCode) {
  if (!provinceCode || !LOCATION_DATA[provinceCode]) {
    return []
  }

  return Object.keys(LOCATION_DATA[provinceCode].cities).map((code) => ({
    code,
    name: LOCATION_DATA[provinceCode].cities[code].name,
  }))
}

/**
 * 根据城市编码获取所有区县列表
 * @param {string} cityCode - 城市编码
 * @returns {Array} 区县列表，每项包含code和name
 */
export function getDistrictsByCity(cityCode) {
  const provinceCode = cityCode.substring(0, 2) + '0000'

  if (
    !cityCode ||
    !provinceCode ||
    !LOCATION_DATA[provinceCode] ||
    !LOCATION_DATA[provinceCode].cities[cityCode]
  ) {
    return []
  }

  return Object.keys(
    LOCATION_DATA[provinceCode].cities[cityCode].districts,
  ).map((code) => ({
    code,
    name: LOCATION_DATA[provinceCode].cities[cityCode].districts[code].name,
  }))
}
