/**
 * 接口名称：需求列表
 * 接口路径：/baas-sale/demand/list
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91808
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqlist91808 {
  /**
   * 线索ID
   */
  clueId?: number
  /**
   * 部门id
   */
  deptId?: number
  /**
   * 需求状态，0：待评审，1：已评审，2：搁置，3：关闭
   */
  demandStatus?: number
  /**
   * 售前对接人UID
   * private Long preDockingUid;
   */
  pageSize?: number
  pageIndex?: number
  /**
   * 当前登录人userId cookie中获取
   * @NotNull(message = "loginOnUserId不能为空")
   */
  loginOnUserId?: number
  /**
   * 当前登录人所在租户 cookie中获取
   */
  loginOnOrgId?: number
  /**
   * 当前登录人所在租户 cookie中获取
   */
  loginOnUsername?: string
}
export class IReslist91808 {
  code?: number
  msg?: string
  data?: {
    total?: number
    rows?: {
      /**
       * 需求Id
       */
      id?: number
      /**
       * 客户名称
       */
      customerCompany?: string
      /**
       * 创建人姓名/需求反馈人员
       */
      createrName?: string
      /**
       * 创建时间/需求提交时间
       */
      gmtCreate?: string
      /**
       * 需求
       */
      demand?: string
      /**
       * 需求状态，0：待评审，1：已评审，2：搁置，3：关闭
       */
      demandStatus?: number
    }[]
  }
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const list91808: Serve<IReqlist91808, IReslist91808> = (
  data?,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) =>
  request<IReslist91808>(
    {
      method: 'post',
      url: '/web-awg/baas-sale/demand/list',
      data,
      ...options,
    },
    serviceConfig,
  )
