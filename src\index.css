/*@unocss preflights;*/
/*@unocss default;*/
@unocss;

body {
    width: 100% !important;
    background: rgba(233, 236, 240, 1) !important;
}

#root {
    height: 100%;
}

.xempty__desc {
    width: 110%;
}

.ant-btn {
    border: 1px #ccc solid;
    padding: 7px 15px;
    border-radius: 4px;
}

.h-100 {
    height: 100% !important;
}

.my-prefix-layout {
    height: 100%;

    td {
        font-weight: normal;
    }
}

.business-ant-layout {
    height: 100%;
}

.business-ant-pro-page-container {
    height: 100%;

    .business-ant-pro-grid-content, .business-ant-pro-grid-content-children, .business-ant-pro-page-container-children-container, .business-ant-pro-card {
        height: 100%;
    }
}

.business-ant-page-header-breadcrumb li:first-child {
    cursor: pointer;
}

.business-ant-page-header.business-ant-page-header-ghost {
    background-color: #F7F8F9 !important;
}

.business-ant-pro-global-header-right-content {
    padding-right: 16px;
}

.business-ant-table-wrapper .business-ant-table-thead > tr > th, .business-ant-table-wrapper .business-ant-table-thead > tr > td {
    color: #5C626B
}

.business-ant-modal-content .business-ant-modal-body .business-ant-btn-primary:focus {
    box-shadow: none !important;
    outline: none !important;
}

.xmicon {
    vertical-align: inherit !important;
}

.my-prefix-top-nav-header-menu {
    padding: 0 !important;
    line-height: 55px;
}

.my-prefix-top-nav-header-logo > *:first-child > img {
    height: 20px;
}

.business-ant-menu-item,
.business-ant-menu-submenu {
    background: white !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin-left: 24px !important;
}

/*[&_.business-ant-menu-item]:rounded-0 [&_.business-ant-menu-item]:p-0 [&_.business-ant-menu-item]:m-l-24px [&_.business-ant-menu-item-selected]:!c-[var(--xm-primary-color)]*/
.business-ant-menu-item:hover,
.business-ant-menu-submenu:hover {
    background: transparent !important;
}

.business-ant-menu-item-selected,
.business-ant-menu-submenu-selected,
.business-ant-menu-submenu-selected .business-ant-menu-submenu-title {
    color: var(--xm-primary-color) !important;
    background: transparent !important;

    &:after {
        width: 100%;
        border-bottom-width: 2px !important;
        border-bottom-color: var(--xm-primary-color) !important;
        inset-inline: 0 !important;
    }
}

.business-ant-page-header-heading {
    display: none !important;
}

.business-ant-breadcrumb {
    padding: 0 16px 0 16px !important;
}

.business-ant-pro-card-body {
    padding-inline: 16px !important;
    padding-block: 16px;
}

.business-ant-pro-card-header {
    padding-inline: 16px !important;
}

.business-ant-menu-submenu-popup {
    display: none !important;
}

.business-ant-page-header-no-children {
    height: 0px !important;
}

.business-ant-pro-page-container-children-container {
    padding: 0 !important;
}

.business-ant-pro-table .business-ant-pro-table-search {
    position: relative;
    margin-block-end: 16px;

    .business-ant-pro-query-filter {
        padding: 0 !important;
    }

    .business-ant-pro-query-filter-row {
        row-gap: 0;
    }

    .business-ant-pro-query-filter-row-split {
        .business-ant-row {
            display: flex !important;
        }

        .business-ant-row .business-ant-col {
            &.business-ant-form-item-label {
                flex-basis: min-content !important;
                padding-left: 12px;
            }

            &.business-ant-form-item-control {
                flex: 1;
                max-width: 100% !important;
            }
        }

        .business-ant-form-item-control, .business-ant-form-item-label {
            line-height: 32px;
        }

        .business-ant-form-item {
            border: 1px solid #cbcfd6;
            border-radius: 6px;
            margin: 0 !important;

            .business-ant-form-item-label > label {
                color: #959ba3;
            }

            .business-ant-picker,
            .business-ant-select-selector,
            .business-ant-input-affix-wrapper,
            .business-ant-input-number {
                border: none;
                outline: none;
                box-shadow: none;
            }
        }
    }
}

.page-bottom-button {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.15);
}

.page-bottom-button .ant-space,
.page-bottom-button button {
    width: 100%;
}

.h5 .page-bottom-button .ant-space-item {
    width: 50%;
}

.h5 {
    @media screen and (max-width: 575px) {
        .ant-pro-form > div:not(.ant-pro-form-light-filter) .pro-field {
            width: calc(100vw - 40px);
            max-width: 100%;
        }
    }
}

.chat pre:not([contenteditable]) {
    width: 100%;
    overflow: auto;
    background: #f7f8f9;
    border-radius: 6px;
    padding: 10px;
}
