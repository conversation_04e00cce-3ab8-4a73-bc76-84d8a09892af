import { Card, Descriptions, Skeleton, Space, Typography } from 'antd'
import { useHistory } from 'react-router-dom'
import Comments from '@/components/Comments'
import { detail91809 } from '@/apis/project3347/detail91809'
import XmIconReact from '@xm/icons-ai/react-svg'
import { useRequest } from 'ahooks'
import { getLabelByValue } from '@/utils'
import { budgetStatus, industryOptions } from '@/views/clues/constants'
import { getClue91846, getCommentTypeId91866 } from '@/apis/project3347'
import { acceptanceResult, demindStatus } from '@/views/demind/constants'
import dayjs from 'dayjs'
import { renderDemindStatus } from '@/views/demind/index'
import { useState } from 'react'

const { Title, Paragraph, Text } = Typography

const CustomerDetail = () => {
  const history = useHistory()
  const searchParams = new URLSearchParams(history.location.search)
  const [id, setId] = useState(searchParams.get('id'))
  const commentId = searchParams.get('commentId')
  const {
    data: detail = {},
    loading,
    run,
  } = useRequest(
    async () => {
      let _id = id
      if (!id && commentId) {
        const { data } = await getCommentTypeId91866(
          {
            id: commentId,
          },
          {
            hideMessage: true,
          },
        )

        _id = data
        setId(_id)
      }
      const { data } = await detail91809({
        id: _id,
      })

      if (data?.clueId) {
        getClueDetail(data?.clueId)
      }

      return data
    },
    {
      onError: (e) => {
        if (e.code === 500) {
          history.replace('/error/404')
        }
      },
    },
  )

  const {
    data: clueDetail = {},
    loading: getClueLoading,
    run: getClueDetail,
  } = useRequest(
    async (clueId) => {
      const { data } = await getClue91846({
        id: clueId,
      })

      return data
    },
    {
      manual: true,
    },
  )

  return (
    <Skeleton active loading={loading}>
      <div className="bg-white py-20 rounded-4 px-24">
        <div className="flex-col  mb-16 md:flex-row md:justify-between md:items-start">
          <div className="flex-col gap-8">
            {renderDemindStatus(detail.demandStatus)}
            <Title level={4} className="!mb-0 flex-1">
              {detail.title || '等待模型生成中'}
            </Title>
          </div>

          {!process.env.PUBLIC_H5 ? (
            <Space className="mt-8 md:mt-0">
              <span
                className="flex items-center cursor-pointer text-primary"
                onClick={() => {
                  history.push(`/demind/create/?id=${id}`)
                }}
              >
                <XmIconReact
                  className="mr-4"
                  color="var(--xm-primary-color)"
                  name="bianji_line"
                />
                编辑
              </span>
            </Space>
          ) : null}
        </div>
        <Card
          title="线索信息"
          className="!shadow-none"
          bordered={false}
          styles={{
            header: {
              borderBottom: 0,
              paddingInline: 0,
            },
            body: {
              paddingTop: 0,
              paddingInline: 0,
            },
          }}
        >
          <Skeleton active loading={getClueLoading}>
            <Descriptions column={3} layout="vertical">
              <Descriptions.Item label="客户名称">
                {clueDetail.customerCompany || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="所在区域">
                {clueDetail.regionName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="行业">
                {getLabelByValue(clueDetail.industry, industryOptions)}
              </Descriptions.Item>
              <Descriptions.Item label="项目金额">
                {clueDetail.projectAmount || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="预算到位情况">
                {getLabelByValue(clueDetail.budgetPlace, budgetStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="资金来源">
                {clueDetail.fundSource || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="客户是否立项">
                {clueDetail.expectSigningDate
                  ? dayjs(clueDetail.expectSigningDate).format('YYYY-MM-DD')
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="预计签单时间">
                {clueDetail.expectSigningDate || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="需求反馈人">
                {clueDetail.createrName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="提交时间">
                {clueDetail.gmtCreate || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="需求提出者身份及角色	">
                {clueDetail.demandProposeStaff || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="需求提出者是否具有决策权	">
                {clueDetail.demandProposeDecision || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Skeleton>
        </Card>

        <Card
          title="需求信息"
          className="!shadow-none"
          bordered={false}
          styles={{
            header: {
              borderBottom: 0,
              paddingInline: 0,
            },
            body: {
              paddingTop: 0,
              paddingInline: 0,
            },
          }}
        >
          <Descriptions column={3} layout="vertical">
            <Descriptions.Item label="需求" span={24}>
              <pre className="break-words leading-[1.5] whitespace-pre-line">
                {detail.demand || '-'}
              </pre>
            </Descriptions.Item>
            <Descriptions.Item label="具体业务场景" span={24}>
              <pre className="break-words leading-[1.5] whitespace-pre-line">
                {detail.businessScene || '-'}
              </pre>
            </Descriptions.Item>
            <Descriptions.Item label="当前业务痛点" span={24}>
              <pre className="break-words leading-[1.5] whitespace-pre-line">
                {detail.businessPain || '-'}
              </pre>
            </Descriptions.Item>
            <Descriptions.Item label="需求实现时间要求">
              {detail.achieveDate || '-'}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>

      <Card
        title="反馈信息"
        className="!shadow-none my-16"
        bordered={false}
        styles={{
          header: {
            borderBottom: 0,
          },
          body: {
            paddingTop: 0,
          },
        }}
      >
        <Descriptions column={1} layout="vertical">
          <Descriptions.Item label="反馈评审信息">
            {detail.productFeedback || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="生态评审信息">
            {detail.ecologyFeedback || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="需求状态">
            {getLabelByValue(detail.demandStatus, demindStatus) || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="受理结果">
            {getLabelByValue(detail.acceptanceStatus, acceptanceResult) || '-'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Comments
        onRefresh={async () => {
          const { data } = await detail91809({
            id,
          })
          return data.commitList
        }}
        id={id}
        type={1}
        comments={detail?.commitList}
      />
    </Skeleton>
  )
}

export default CustomerDetail
