import Native from '@xm/native'
import { createApp } from '@xm/xapp-main'
import routes from '@/router'
import './index.css'
import '@unocss/reset/tailwind-compat.css'
import { HoxRoot } from 'hox'
import 'dayjs/locale/zh-cn'
import { RoleStoreProvider } from '@/context/useRoleStore'

window.xm = new Native()

// const Layout = process.env.PUBLIC_H5 ? LayoutH5 : LayoutPc
const app = createApp({
  el: '#root',
  router: {
    routes,
  },
  plugins: [
    () => {
      if (process.env.PUBLIC_H5) {
        // 通过ua判断是ios还是安卓
        const isAndroid = /android/.test(navigator.userAgent.toLowerCase())
        if (!isAndroid) {
          let startY = 0

          document.body.style.cssText = 'height:calc(100vh - 34px)!important;'
          window.addEventListener(
            'touchstart',
            (e) => {
              // 当本次滑动距离超过 50 像素时，调用 hideKeyboard 方法隐藏键
              let touch = e.targetTouches[0]
              //滑动起点的坐标
              startY = touch.pageY
            },
            { passive: false },
          )
          window.addEventListener('touchmove', (e) => {
            // 当本次滑动距离超过 50 像素时，调用 hideKeyboard 方法隐藏键
            let touch = e.targetTouches[0]
            //手势滑动时，手势坐标不断变化，取最后一点的坐标为最终的终点坐标
            if (Math.abs(touch.pageY - startY) > 50) {
              window.xm?.hideKeyboard()
            }
          })
        } else {
          document.body.style.cssText = 'height:100vh!important;'
        }
      } else {
        // 加载 css https://saas-dev.uban360.com/statics/config/??app.css?_t=1706598431119
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = '/statics/config/??app.css'
        document.head.appendChild(link)
      }
    },
  ],
  render: ({ children }) => {
    return (
      <HoxRoot>
        <RoleStoreProvider>{children}</RoleStoreProvider>
      </HoxRoot>
    )
  },
})

if (module.hot) {
  module.hot.accept((err) => {
    window.top.location.reload()
  })
}

export default app
