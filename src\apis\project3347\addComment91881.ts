/**
 * 接口名称：新增评论
 * 接口路径：/baas-sale/comment/addComment
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91881
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqaddComment91881 {
  /**
   * 评论内容
   */
  content?: string
  /**
   * 评论中@的uid
   */
  uids?: number[]
  /**
   * 线索或需求的ID
   */
  id?: number
  /**
   * 类型，0：线索，1：需求
   */
  type?: number
}
export class IResaddComment91881 {
  code?: number
  msg?: null
  data?: null
  success?: boolean
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const addComment91881: Serve<
  IReqaddComment91881,
  IResaddComment91881
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResaddComment91881>(
    {
      method: 'post',
      url: '/web-awg/baas-sale/comment/addComment',
      data,
      ...options,
    },
    serviceConfig,
  )
