/**
 * 接口名称：静态知识库
 * 接口路径：/baas-sale/staticKnowledge/stream/getStaticKnowledge
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91819
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetStaticKnowledge91819 {
  /**
   * 1 线索问题 2 需求问答 3 知识库问答
   */
  type: number
  /**
   * 输入内容
   */
  userInput?: string
}
export class IResgetStaticKnowledge91819 {
  timeout?: number
  handler?: {}
  earlySendAttempts?: {
    data?: {}
    mediaType?: {
      type?: string
      subtype?: string
      parameters?: {
        key?: string
      }
      toStringValue?: string
    }
  }[]
  complete?: boolean
  failure?: {
    /**
     * Specific details about the Throwable.  For example, for
     * {@code FileNotFoundException}, this contains the name of
     * the file that could not be found.
     */
    detailMessage?: string
    /**
     * The throwable that caused this throwable to get thrown, or null if this
     * throwable was not caused by another throwable, or if the causative
     * throwable is unknown.  If this field is equal to this throwable itself,
     * it indicates that the cause of this throwable has not yet been
     * initialized.
     */
    cause?: string
    /**
     * The stack trace, as returned by{@link #getStackTrace()}.
     *
     * The field is initialized to a zero-length array.  A{@code
     *     * null} value of this field indicates subsequent calls to{@link
     *     * #setStackTrace(StackTraceElement[])} and{@link
     *     * #fillInStackTrace()} will be no-ops.
     */
    stackTrace?: {
      /**
       * Normally initialized by VM
       */
      classLoaderName?: string
      moduleName?: string
      moduleVersion?: string
      declaringClass?: string
      methodName?: string
      fileName?: string
      lineNumber?: number
      /**
       * Default to show all
       */
      format?: number
    }[]
    /**
     * The list of suppressed exceptions, as returned by{@link
     *     * #getSuppressed()}.  The list is initialized to a zero-element
     * unmodifiable sentinel list.  When a serialized Throwable is
     * read in, if the{@code suppressedExceptions} field points to a
     * zero-element list, the field is reset to the sentinel value.
     */
    suppressedExceptions?: {
      /**
       * Specific details about the Throwable.  For example, for
       * {@code FileNotFoundException}, this contains the name of
       * the file that could not be found.
       */
      detailMessage?: string
      /**
       * The throwable that caused this throwable to get thrown, or null if this
       * throwable was not caused by another throwable, or if the causative
       * throwable is unknown.  If this field is equal to this throwable itself,
       * it indicates that the cause of this throwable has not yet been
       * initialized.
       */
      cause?: string
      /**
       * The stack trace, as returned by{@link #getStackTrace()}.
       *
       * The field is initialized to a zero-length array.  A{@code
       *     * null} value of this field indicates subsequent calls to{@link
       *     * #setStackTrace(StackTraceElement[])} and{@link
       *     * #fillInStackTrace()} will be no-ops.
       */
      stackTrace?: {
        /**
         * Normally initialized by VM
         */
        classLoaderName?: string
        moduleName?: string
        moduleVersion?: string
        declaringClass?: string
        methodName?: string
        fileName?: string
        lineNumber?: number
        /**
         * Default to show all
         */
        format?: number
      }[]
      /**
       * The list of suppressed exceptions, as returned by{@link
       *     * #getSuppressed()}.  The list is initialized to a zero-element
       * unmodifiable sentinel list.  When a serialized Throwable is
       * read in, if the{@code suppressedExceptions} field points to a
       * zero-element list, the field is reset to the sentinel value.
       */
      suppressedExceptions?: {}[]
    }[]
  }
  sendFailed?: boolean
  timeoutCallback?: {
    delegate?: {}
  }
  errorCallback?: {
    delegate?: {}
  }
  completionCallback?: {
    delegate?: {}
  }
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getStaticKnowledge91819: Serve<
  IReqgetStaticKnowledge91819,
  IResgetStaticKnowledge91819
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetStaticKnowledge91819>(
    {
      method: 'post',
      url: '/web-awg/baas-sale/staticKnowledge/stream/getStaticKnowledge',
      data,
      ...options,
    },
    serviceConfig,
  )
