// ... 已有代码 ...
const fs = require('fs')
// const AdmZip = require('adm-zip')

// 1. 拷贝plugin.json到dist目录
// fs.copyFileSync('./plugin.json', './dist/plugin.json')
fs.copyFileSync('./static/vconsole.min.js', './dist/vconsole.min.js')
fs.copyFileSync('./pc-redirect.html', './dist/pc-redirect.html')
// fs.copyFileSync('./static/xmmp.min.js', './dist/xmmp.min.js')

// 2. 创建ZIP压缩文件
// const zip = new AdmZip()
// zip.addLocalFolder('./dist')
// zip.writeZip('./dist.zip')

// ... 后续可能存在其他构建后处理代码 ...
// 新增移动文件逻辑
// const readline = require('readline').createInterface({
//   input: process.stdin,
//   output: process.stdout,
// })
//
// readline.question('是否要将 dist.zip 移动到下载目录？(y/n) ', (answer) => {
//   if (answer.toLowerCase() === 'y') {
//     const os = require('os')
//     const path = require('path')
//     const downloadsPath = path.join(os.homedir(), 'Downloads', 'dist.zip')
//
//     try {
//       fs.renameSync('./dist.zip', downloadsPath)
//       console.log('文件已移动到下载目录')
//     } catch (err) {
//       console.error('移动文件失败:', err.message)
//     }
//   }
//   readline.close()
// })
