/**
 * 接口名称：获取项目管理列表
 * 接口路径：/project/statistics/management/list
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93656
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetProjectList93656 {}
export class IResgetProjectList93656 {
  success?: boolean
  code?: number
  msg?: string
  data?: object
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getProjectList93656: Serve<
  IReqgetProjectList93656,
  IResgetProjectList93656
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetProjectList93656>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/project/statistics/management/list',
      data,
      ...options,
    },
    serviceConfig,
  )
