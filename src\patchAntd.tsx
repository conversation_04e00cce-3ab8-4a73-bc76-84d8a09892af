export * from 'antd'

import React, { useEffect, useMemo } from 'react'
import { mainWindow } from '@xm/xapp-main'
import { Modal as AntModal } from 'antd'
const patchModalOffsetWrapClassName = 'patch-modal-wrap'
const needPatch = () => !/noNav/.test(location.search)

function getSandboxIframe() {
  return window.__XAPP__?.sandbox?.iframe
}

function getRandom() {
  return Math.ceil(Math.random() * 1000000)
}

/** 处理主项目遮罩 */
const clickDispatchCacheMap: { [k: string]: () => void } = {}
const patchModalName = 'patch-modal-mask'
export function toggleMainAppMask(show: boolean, modalId: string) {
  const cls = `${patchModalName}__${modalId}`
  const antdModalMaskBackgroundColor = 'rgba(0,0,0,.45)'

  const document = mainWindow!.document
  let mainAppMask = document.querySelector(`.${cls}`)
  mainAppMask?.childNodes.forEach((div) => {
    ;(div as HTMLDivElement).style.setProperty('background', '')
  })

  // 处理 mask 点击事件
  const addEvents = (add = true) => {
    mainAppMask?.childNodes.forEach((maskChild) => {
      if (add) {
        clickDispatchCacheMap[modalId] = () => {
          // 触发当前应用 window 内的当前弹窗的点击事件
          const el = window.document.querySelector(`.${modalId}`)
          // 兼容公司内部的antd类名
          if (
            el?.classList.contains('business-ant-modal') ||
            el?.classList.contains('xm-modal')
          ) {
            ;(el.parentNode as HTMLDivElement)?.click()
          } else if (
            el?.classList.contains('business-ant-drawer') ||
            el?.classList.contains('xm-drawer')
          ) {
            ;(el.childNodes[0] as HTMLDivElement)?.click()
          } else {
            console.log('[xapp-sandbox]: patchModal 未处理的节点点击类型')
          }
        }
      } else {
        delete clickDispatchCacheMap[modalId]
      }
      maskChild[add ? 'addEventListener' : 'removeEventListener'](
        'click',
        clickDispatchCacheMap[modalId],
      )
    })
  }

  function _addMask() {
    mainAppMask = document.createElement('div')
    mainAppMask.classList.add(patchModalName)
    mainAppMask.classList.add(cls)
    // antd 的 z-index: 1010
    const maskCommonStyle = 'position:absolute;z-index:1000;'
    // @ts-ignore
    const rootIframe = getSandboxIframe()

    // 顶部遮罩
    let topMask: HTMLDivElement | undefined
    // @ts-ignore
    const rootIframeTop = rootIframe?.style?.top
    if (rootIframeTop && parseInt(rootIframeTop)) {
      topMask = document.createElement('div')
      topMask.style.cssText = `${maskCommonStyle}right:0;top:0;width:100%;height:${rootIframeTop};`
      topMask!.style.setProperty('background', antdModalMaskBackgroundColor)
    }
    if (topMask) {
      mainAppMask.appendChild(topMask)
    }

    // 左侧遮罩
    let leftMask: HTMLDivElement | undefined
    const rootIframeLeft = rootIframe?.style?.left
    if (rootIframeLeft && parseInt(rootIframeLeft)) {
      const height = topMask ? `calc(100vh - ${rootIframeTop})` : '100vh'
      const top = topMask ? rootIframeTop : 0
      leftMask = document.createElement('div')
      leftMask.style.cssText = `${maskCommonStyle}left:0;top:${top};width:${rootIframeLeft};height:${height};`
      leftMask!.style.setProperty('background', antdModalMaskBackgroundColor)
    }
    if (leftMask) {
      mainAppMask.appendChild(leftMask)
    }

    // 右侧遮罩
    let rightMask: HTMLDivElement | undefined
    // @ts-ignore
    const rootIframeRight = rootIframe?.style?.right
    if (rootIframeRight && parseInt(rootIframeRight)) {
      rightMask = document.createElement('div')
      const top = topMask ? rootIframeTop : 0
      const height = topMask ? `calc(100vh - ${rootIframeTop})` : '100vh'
      const width =
        document.body.clientWidth -
        (rootIframeLeft ? parseInt(rootIframeLeft) : 0) -
        parseInt(rootIframe?.style?.width ?? 0)
      rightMask.style.cssText = `${maskCommonStyle}right:0;top:${top};width:${width}px;height:${height};`
      rightMask!.style.setProperty('background', antdModalMaskBackgroundColor)
    }
    if (rightMask) {
      mainAppMask.appendChild(rightMask)
    }

    if (mainAppMask.childNodes.length) {
      document.body.appendChild(mainAppMask)
    }
  }

  if (show) {
    _addMask()
    addEvents()
  } else {
    addEvents(false)
    mainAppMask?.remove()
  }
}

// 在应用隐藏或切换时，清除遮罩
export function clearMainAppMask() {
  // 清除事件
  Object.keys(clickDispatchCacheMap).forEach((modalId) => {
    const cls = `${patchModalName}__${modalId}`
    const parent = (window.top || window).document.querySelector(`.${cls}`)
    parent?.childNodes.forEach((child) => {
      child?.removeEventListener('click', clickDispatchCacheMap[modalId])
    })
    delete clickDispatchCacheMap[modalId]
  })
  // 移除节点
  const mainAppMaskArr = Array.from(
    (window.top || window).document.querySelectorAll(`.${patchModalName}`),
  )
  mainAppMaskArr.forEach((mainAppMask) => {
    mainAppMask?.remove()
  })
  // todo 收集打开的 modal
  //  在清除时手动调用 destroy
}

/** 处理弹窗的位置 */
function patchModalOffset() {
  const id = 'patch-modal-offset'
  let style = document.getElementById(id)
  style?.remove()

  if (!needPatch()) {
    return
  }

  const rootIframe = getSandboxIframe()
  const rootIframeLeft = parseInt(rootIframe?.style?.left ?? '0')
  style = document.createElement('style')
  style.id = id
  const antMessageStyleContent = `.xm-message .xm-message-notice-content, .business-ant-message .business-ant-message-notice-content { transform: translateX(${
    -rootIframeLeft / 2
  }px) }`
  const styleContent = `{ transform:translateX(${
    -rootIframeLeft / 2
  }px);transform-origin:0!important;-ms-overflow-style: none; } ${antMessageStyleContent}`
  // antd 3 没有 wrapClassName，所以样式要作用于 className
  const styleClass = `.${patchModalOffsetWrapClassName} .business-ant-modal, .${patchModalOffsetWrapClassName} .xm-modal`
  style.innerHTML = `${styleClass} ${styleContent} ${styleClass
    .split(',')
    .map((x) => `${x}::-webkit-scrollbar { display: none; }`)
    .join('')}`

  document.body.appendChild(style)
}

/** 处理方法调用的遮罩 */
function patchModalMethod(
  name: 'confirm' | 'success' | 'info' | 'error' | 'warning',
) {
  return (props: any) => {
    const id = `patch-modal-${name}__${getRandom()}`
    toggleMainAppMask(true, id)
    const patchCallback =
      (name: 'onOk' | 'onCancel') =>
      (...args: any) => {
        const res = props[name]?.apply(null, args)
        if (res instanceof Promise) {
          return res.then((r) => {
            toggleMainAppMask(false, id)
            return r
          })
        }
        toggleMainAppMask(false, id)
      }
    patchModalOffset()

    return AntModal[name]({
      ...props,
      onOk: patchCallback('onOk'),
      onCancel: patchCallback('onCancel'),
      /** antd3 源码此处传空还是会被赋值 */
      transitionName: '',
      maskTransitionName: '',
      wrapClassName: patchModalOffsetWrapClassName,
    })
  }
}

const ProxyModal: any = (props: any) => {
  const cls = useMemo(() => `patch-modal-${getRandom()}`, [])

  const open = props.visible || props.open
  const proxyModal =
    needPatch() && props.mask !== false && props.maskMainApp !== false

  useEffect(() => {
    if (proxyModal) {
      toggleMainAppMask(open, cls)
    }
    return () => {
      toggleMainAppMask(false, cls)
    }
  }, [open])

  if (!proxyModal) {
    return <AntModal {...props} />
  }

  patchModalOffset()
  return (
    <AntModal
      {...props}
      className={`${cls}${props.className ? ` ${props.className}` : ''}`}
      wrapClassName={`${patchModalOffsetWrapClassName}${
        props.wrapClassName ? ` ${props.wrapClassName}` : ''
      }`}
      transitionName=""
      maskTransitionName=""
    />
  )
}

let proxyModal = AntModal
if (needPatch()) {
  const _proxy = (Target: typeof ProxyModal) => {
    const Result = (props: any) => Target(props)
    if (Target === ProxyModal) {
      Result.confirm = patchModalMethod('confirm')
      Result.success = patchModalMethod('success')
      Result.info = patchModalMethod('info')
      Result.error = patchModalMethod('error')
      Result.warn = ProxyModal.warn = patchModalMethod('warning')
      Result.defaultProps = AntModal.defaultProps
      Result.destroyAll = () => {
        clearMainAppMask()
        AntModal.destroyAll()
      }
      Result.propTypes = AntModal.propTypes
    }
    return Result
  }

  // @ts-ignore
  proxyModal = _proxy(ProxyModal)
  console.warn(`[patch-antd]: [Modal] has been replaced with patchModal`)
}

export const Modal = proxyModal

// 低版本浏览器 antd 的样式降级了，直接通过 modal.confirm
// 唤起的弹窗没有使用降级方案
// 需要特殊处理
export function useModalConfirm() {
  const [modal, contextHolder] = AntModal.useModal()
  //@ts-ignore
  const handler = window.isLowerChrome ? modal.confirm : Modal.confirm
  //@ts-ignore
  const holder = window.isLowerChrome ? contextHolder : null
  return { modalConfirm: handler, contextHolder: holder }
}
