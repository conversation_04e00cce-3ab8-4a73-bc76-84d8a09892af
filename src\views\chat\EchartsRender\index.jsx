import React, { useMemo, useRef, useState } from 'react'
// import { Button, Dropdown, Modal } from 'antd'
// import XmiconReact from '@xm/icons-ai/dist/react'
// import { saveAs } from 'file-saver'
import { convertToEChartsConfig } from './util'
import ReactEcharts from 'echarts-for-react'
import TableRender from '../TableRender'
// import dayjs from 'dayjs'

const EchartsRender = ({ code, retry, isEnableChat2DB, type }) => {
  const chartRef = useRef()
  const chartModalRef = useRef()
  const [error, setError] = useState(null)
  const [visible, setVisible] = useState(false)
  const chartOption = useMemo(() => {
    if (isEnableChat2DB && code) {
      try {
        const input = JSON.parse(code)
        if (type === 'table') {
          input.chartSchema.chartType = 'Table'
        }
        return convertToEChartsConfig(input)
      } catch (e) {
        console.error(e)
        setError(e)
      }
    }
    // 阶段一：代码块提取
    const codePattern = /```(?:json|javascript)?([\s\S]*?)```/
    const codeMatch = code.match(codePattern)
    if (!codeMatch) {
      setError('无法解析代码块')
      return null
    }

    // 阶段二：配置项解析
    const rawCode = codeMatch[1]
      .trim()
      .replace(/option\s*=\s*/, '') // 移除变量声明
      .replace(/;$/, '') // 移除结尾分号

    let chartOption
    try {
      chartOption = JSON.parse(rawCode) // 优先尝试JSON解析
      setError(false)
    } catch (jsonError) {
      try {
        // 备选方案：通过Function构造器安全解析
        chartOption = new Function('return ' + rawCode)()
        setError(false)
      } catch (jsError) {
        setError(jsonError || jsError)
        console.error('无法解析配置项', rawCode, jsonError, jsError)
      }
    }

    if (chartOption) {
      // 处理一下option，使option中的title与legend不重叠，并且下载、放大等操作不展示
      chartOption.title = {
        top: 0,
        left: 0,
        ...chartOption.title, // 保留用户自定义配置
      }

      // 设置图例定位（标题下方60px处，水平居中）
      chartOption.legend = {
        top: 25,
        left: 0,
        orient: 'horizontal', // 水平排列
        type: 'scroll', // 启用滚动防止过多条目
        ...chartOption.legend,
      }

      // ======== 2. 隐藏工具栏 ========
      // 移除所有工具栏功能（包括下载、数据视图、缩放等）
      chartOption.toolbox = {
        show: false,
        ...chartOption.toolbox,
        feature: {
          saveAsImage: { show: false }, // 隐藏下载
          dataZoom: { show: false }, // 隐藏缩放
          restore: { show: false }, // 隐藏还原
          dataView: { show: false }, // 隐藏数据视图
          ...chartOption.toolbox?.feature,
        },
      }

      // ======== 3. 响应式优化 ========
      // 添加响应式布局配置（可选）
      chartOption.grid = {
        top: 75, // 留出标题和图例空间
        bottom: 30, // 留出dataZoom空间
        containLabel: true,
        left: 10,
        ...chartOption.grid,
      }

      if (Array.isArray(chartOption.dataZoom)) {
        chartOption.dataZoom = chartOption.dataZoom.map((zoom) => ({
          ...zoom,
          bottom: 10, // 统一数据缩放条位置
          height: 15, // 更明显的操作区域
        }))
      } else {
        chartOption.dataZoom = {
          ...chartOption.dataZoom,
          bottom: 10, // 统一数据缩放条位置
          height: 15, // 更明显的操作区域
        }
      }
    }
    return chartOption
  }, [code])

  if (isEnableChat2DB && !chartOption) {
    return <div className="text-14 c-#999">当前图表数据格式不支持</div>
  }

  return chartOption?.type === 'table' ? (
    <TableRender data={chartOption?.data} columns={chartOption?.columns} />
  ) : (
    <div className="relative [&_.ant-image]:hidden">
      {/*<div ref={chartRef} className="w-380 h-240"/>*/}
      <ReactEcharts
        ref={chartRef}
        className="w-[88%] !h-240"
        option={chartOption}
      />
    </div>
  )
}
export default EchartsRender
