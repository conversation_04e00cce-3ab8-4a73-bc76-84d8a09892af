/**
 * 接口名称：获取地区详情
 * 接口路径：/web-awg/baas-sale/api/regions/detail
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93456
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetRegionsDetail93456 {}
export class IResgetRegionsDetail93456 {
  success?: boolean
  code?: number
  msg?: string
  data?: object[]
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getRegionsDetail93456: Serve<
  IReqgetRegionsDetail93456,
  IResgetRegionsDetail93456
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetRegionsDetail93456>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/api/regions/detail',
      data,
      ...options,
    },
    serviceConfig,
  )
