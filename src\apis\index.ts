import { HttpOptions as _HttpOptions } from '@xm/xutil'
import { request as _request } from '@xm/xapp-main'
import { AxiosRequestConfig } from 'axios'
import { message } from 'antd'

type PartialMember =
  | 'baseUrl'
  | 'contentType'
  | 'timeout'
  | 'requestCallback'
  | 'responseCallback'

export type HttpOptions = Omit<_HttpOptions, PartialMember> &
  Partial<Pick<_HttpOptions, PartialMember>>

window.__XAPP_MAIN__.requestOptions.message = {
  ...message,
  error: (content: string) => {
    message.error(content)
  },
}

export function request<T>(params: HttpOptions, config?: AxiosRequestConfig) {
  const { method, url } = params
  // console.log(111, process.env.PUBLIC_H5)
  if (process.env.PUBLIC_H5) {
    // const isAndroid = /android/.test(navigator.userAgent.toLowerCase())
    params.headers = {
      // 如果是安卓添加 csrf-token
      'csrf-token': window.Cookies.get('csrf-token'),
      // 'csrf-token':
      // '2e53f983f1089b9efc84cb831ec1afe8962b0d99607ae6f04fccad668218ddc8',
    }
    // params.miniapp = true
    // @ts-ignore
    window.__XAPP_MAIN__.requestOptions.message = window.xm.toast
    params.responseCallback = [
      (res) => {
        // console.log(1, res)
        return res
      },
      (err) => {
        // window.xm.toast(err.message)
        // console.info(111, err)
        return err
      },
    ]
    // params.isSuccess = (res) => {
    //   // console.log(2, res)
    //   // todo 给到的就是 data 了
    //   return true
    // }
  }
  params.codeFallback = {
    '-702': () => {
      // console.info(1111)
      location.hash = '#/login'
    },
  }
  // console.log(params)
  return _request[method as 'get' | 'post']<T>(url, params.data, params, config)
}

export const uploadFile = async <T>(params: FormData) => {
  return await _request.post<T>('/sfs/webUpload/file', params)
}
