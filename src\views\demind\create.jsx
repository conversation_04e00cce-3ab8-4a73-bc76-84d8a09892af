import {
  Button,
  Card,
  Descriptions,
  message,
  Skeleton,
  Space,
  Spin,
} from 'antd'
import {
  ProCard,
  ProForm,
  ProFormList,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components'
import { useHistory } from 'react-router-dom'
import XmIconReact from '@xm/icons-ai/react-svg'
import { useRef, useState } from 'react'
import { useRequest } from 'ahooks'
import { detail91809 } from '@/apis/project3347/detail91809'
import { edit91815, getClue91846, report91806 } from '@/apis/project3347'
import { getLabelByValue } from '@/utils'
import {
  budgetStatus,
  industryOptions,
  projectStatus,
} from '@/views/clues/constants'
import { DatePicker } from '@/components/DatePicker'
import { acceptanceResult, demindStatus } from '@/views/demind/constants'
import dayjs from 'dayjs'

const CustomerCreate = () => {
  const history = useHistory()
  const actionRef = useRef()
  const formRef = useRef()
  const searchParams = new URLSearchParams(history.location.search)
  const [clueId, setClueId] = useState(searchParams.get('clue'))
  const id = searchParams.get('id')
  const goBack = () => {
    if (process.env.PUBLIC_H5) {
      window.xm?.close()
    } else {
      history.goBack()
    }
  }

  const { loading: submitLoading, run: onFinish } = useRequest(
    async (values) => {
      const { demandReportContentDTOList = [] } = values
      if (id) {
        await edit91815({
          id,
          ...demandReportContentDTOList[0],
          ...values,
        })
      } else {
        await report91806({
          clueId,
          demandReportContentDTOList,
        })
      }
      message.success('提交成功')
      if (process.env.PUBLIC_H5) {
        window.xm?.close()
      } else {
        // 提交表单逻辑
        history.push('/demind')
      }
    },
    {
      manual: true,
    },
  )

  const { data: clueDetail = {}, loading: getClueLoading = true } = useRequest(
    async () => {
      if (clueId) {
        const { data } = await getClue91846({
          id: clueId,
        })
        return data
      }
    },
    {
      refreshDeps: [clueId],
    },
  )

  const { loading } = useRequest(async () => {
    if (id) {
      const { data } = await detail91809({
        id,
      })

      setClueId(data?.clueId)

      if (data) {
        const {
          demand,
          businessPain,
          // demandProposeDecision,
          // demandProposeStaff,
          businessScene,
          achieveDate,
        } = data
        formRef.current?.setFieldsValue({
          ...data,
          demandReportContentDTOList: [
            {
              demand,
              businessPain,
              // demandProposeStaff,
              // demandProposeDecision,
              businessScene,
              achieveDate,
            },
          ],
        })
      }
    }
  })

  return (
    <>
      <Card
        title="线索信息"
        className="!shadow-none"
        bordered={false}
        styles={{
          header: {
            borderBottom: 0,
          },
          body: {
            paddingTop: 0,
          },
        }}
      >
        <Skeleton active loading={getClueLoading}>
          <Descriptions column={3} layout="vertical">
            <Descriptions.Item label="客户名称">
              {clueDetail.customerCompany || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="所在区域">
              {clueDetail.regionName || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="行业">
              {getLabelByValue(clueDetail.industry, industryOptions)}
            </Descriptions.Item>
            <Descriptions.Item label="项目金额">
              {clueDetail.projectAmount || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="预算到位情况">
              {getLabelByValue(clueDetail.budgetPlace, budgetStatus)}
            </Descriptions.Item>
            <Descriptions.Item label="资金来源">
              {clueDetail.fundSource || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="客户是否立项">
              {getLabelByValue(clueDetail.projectApproval, projectStatus)}
            </Descriptions.Item>
            <Descriptions.Item label="预计签单时间">
              {clueDetail.expectSigningDate
                ? dayjs(clueDetail.expectSigningDate).format('YYYY-MM-DD')
                : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="需求反馈人">
              {clueDetail.createrName || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="提交时间">
              {clueDetail.gmtCreate || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="需求提出者身份及角色	">
              {clueDetail.demandProposeStaff || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="需求提出者是否具有决策权	">
              {clueDetail.demandProposeDecision || '-'}
            </Descriptions.Item>
          </Descriptions>
        </Skeleton>
      </Card>
      <Spin spinning={loading}>
        <ProForm
          className={`mt-8 ${process.env.PUBLIC_H5 ? '' : 'mb-60'}`}
          onFinish={onFinish}
          initialValues={{
            demandReportContentDTOList: [{}],
          }}
          formRef={formRef}
          submitter={false}
        >
          <ProFormList
            className="w-full [&_.business-ant-pro-form-list-container]:w-full mb-8"
            name="demandReportContentDTOList"
            actionRef={actionRef}
            creatorButtonProps={false}
            copyIconProps={false}
            deleteIconProps={false}
            alwaysShowItemLabel
            containerStyle={{
              width: '100%',
            }}
            itemContainerRender={(doms, { index, fields }) => {
              return (
                <ProCard
                  title={`需求信息${id ? '' : index + 1}`}
                  bodyStyle={{
                    paddingBottom: 0,
                  }}
                  className="w-full my-8"
                >
                  {doms}

                  {fields.length > 1 ? (
                    <span
                      className="absolute top-24 right-24 flex items-center cursor-pointer text-#959BA3 text-14"
                      onClick={() => {
                        actionRef.current?.remove(index)
                      }}
                    >
                      <XmIconReact
                        className="mr-4 flex items-center"
                        size={14}
                        color="#959BA3"
                        name="deleted_line"
                      />
                      删除
                    </span>
                  ) : null}
                </ProCard>
              )
            }}
          >
            <ProFormTextArea
              name="demand"
              label="需求"
              placeholder="请输入内容"
              rules={[{ required: true, message: '请输入需求' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProFormTextArea
              name="businessScene"
              label="具体业务场景"
              placeholder="请输入内容"
              rules={[{ required: true, message: '请输入具体业务场景' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProFormTextArea
              name="businessPain"
              label="当前业务痛点"
              placeholder="这个需求是怎么产生的，客户是出于什么考虑而反馈的需求。比如是因为上级领导要求或者是实际业务需要"
              rules={[{ required: true, message: '请输入当前业务痛点' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProForm.Group>
              <ProForm.Item
                name="achieveDate"
                label="需求实现时间要求"
                rules={[{ required: true, message: '请选择需求实现时间要求' }]}
              >
                <DatePicker
                  fieldProps={{
                    className: 'pro-field pro-field-md',
                  }}
                  noStyle
                  placeholder="请选择需求实现时间要求"
                />
              </ProForm.Item>
              {/*<ProFormText*/}
              {/*    name="demandProposeStaff"*/}
              {/*    label="需求提出者身份及角色"*/}
              {/*    width="md"*/}
              {/*    readonly*/}
              {/*/>*/}
              {/*<ProFormText*/}
              {/*    name="demandProposeDecision"*/}
              {/*    label="需求提出者是否具有决策权"*/}
              {/*    width="md"*/}
              {/*/>*/}
            </ProForm.Group>
          </ProFormList>

          {id ? (
            <ProCard title="需求反馈信息" className="mb-8">
              <ProFormTextArea
                name="productFeedback"
                label="产研评审意见"
                placeholder="请输入内容"
                fieldProps={{
                  showCount: true,
                  maxLength: 500,
                }}
              />
              <ProFormTextArea
                name="ecologyFeedback"
                label="生态评审意见"
                placeholder="请输入内容"
                fieldProps={{
                  showCount: true,
                  maxLength: 500,
                }}
              />
              <ProFormRadio.Group
                radioType="button"
                name="demandStatus"
                label="需求状态"
                width="md"
                fieldProps={{
                  className: `flex flex-wrap gap-8 [&_.business-ant-radio-button-wrapper]:rounded-4 [&_.ant-radio-button-wrapper]:rounded-4`,
                }}
                options={demindStatus.slice(1, demindStatus.length)}
              />
              <ProFormRadio.Group
                radioType="button"
                name="acceptanceStatus"
                label="受理结果"
                width="md"
                fieldProps={{
                  className: `flex flex-wrap gap-8 [&_.business-ant-radio-button-wrapper]:rounded-4 [&_.ant-radio-button-wrapper]:rounded-4`,
                }}
                options={acceptanceResult}
              />
            </ProCard>
          ) : null}
        </ProForm>
        {!id ? (
          <Button
            className="mb-16 border-none rounded-4 text-#2277FF [&:hover_path]:fill-[var(--xm-primary-color)]"
            block
            onClick={() => {
              actionRef.current?.add({}, 1)
            }}
          >
            <XmIconReact
              className="flex items-center"
              size={14}
              color="#2277FF"
              name="plus_linear_line"
            />
            添加需求
          </Button>
        ) : null}
        <div className="page-bottom-button flex bg-#fff w-full px-16 py-14 shadow-[0_1px_0_0_#FFFFFF]">
          <Space>
            <Button
              loading={submitLoading}
              type="primary"
              onClick={() => formRef?.current?.submit()}
            >
              提交
            </Button>
            <Button disabled={submitLoading} onClick={goBack}>
              取消
            </Button>
          </Space>
        </div>
      </Spin>
    </>
  )
}

export default CustomerCreate
