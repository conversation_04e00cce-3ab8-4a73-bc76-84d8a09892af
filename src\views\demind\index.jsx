import { ProTable } from '@ant-design/pro-components'

import { useRef } from 'react'
import { Badge, Button, Space } from 'antd'
import { useHistory } from 'react-router-dom'
import { demindStatus } from '@/views/demind/constants'
import UserSelector from '@/components/UserSelector'
import { useRoleStore } from '@/context/useRoleStore'
import { list91808 } from '@/apis/project3347/list91808'

export const renderDemindStatus = (value) => {
  const status = demindStatus.find((item) => item.value === +value)
  return <Badge color={status?.color} text={status?.label || '-'} />
}

const Index = () => {
  const history = useHistory()
  const tableRef = useRef()
  const { hasDeptSelect } = useRoleStore()
  const formRef = useRef()

  // 表格列定义
  const columns = [
    hasDeptSelect && {
      title: '部门',
      dataIndex: 'deptId',
      key: 'deptId',
      hideInTable: true,
      renderFormItem: (_, props) => {
        const value = formRef.current?.getFieldValue('deptId')
        return (
          <UserSelector
            title="选择部门"
            onChange={(val) => {
              formRef.current.setFieldsValue({ deptId: val })
              formRef.current.submit()
            }}
            value={value}
            allowClear
            treeProps={{
              orgTree: {
                add: true,
                isopen: true,
                chooseDept: true,
                onlyDept: true,
              },
            }}
          />
        )
      },
    },
    hasDeptSelect && {
      title: '售前负责人',
      dataIndex: 'preDockingUid',
      key: 'preDockingUid',
      hideInTable: true,
      renderFormItem: (_, props) => {
        const value = formRef.current?.getFieldValue('preDockingUid')
        return (
          <UserSelector
            allowClear
            title="选择售前负责人"
            onChange={(val) => {
              formRef.current.setFieldsValue({ preDockingUid: val })
              formRef.current.submit()
            }}
            value={value}
          />
        )
      },
    },
    {
      title: '客户名称',
      dataIndex: 'customerCompany',
      key: 'customerCompany',
      search: false,
      ellipsis: true,
    },
    {
      title: '需求反馈人员',
      dataIndex: 'createrName',
      key: 'createrName',
      search: false,
      ellipsis: true,
    },
    {
      title: '需求提交时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      search: false,
      ellipsis: true,
    },
    {
      title: '需求',
      dataIndex: 'demand',
      key: 'demand',
      search: false,
      ellipsis: true,
    },
    {
      title: '需求状态',
      dataIndex: 'demandStatus',
      key: 'demandStatus',
      valueType: 'select',
      fieldProps: {
        onChange() {
          formRef.current.submit()
        },
        options: demindStatus,
      },
      render: (_, record) => {
        return renderDemindStatus(record.demandStatus)
      },
    },
    {
      title: '操作',
      key: 'action',
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            type="link"
            onClick={() => {
              history.push(`/demind/detail/?id=${record.id}`)
            }}
          >
            详情
          </Button>
          <Button
            size="small"
            type="link"
            onClick={() => {
              history.push(`/demind/create/?id=${record.id}`)
            }}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ].filter(Boolean)

  return (
    <div className="bg-white p-16 rounded-md">
      <ProTable
        formRef={formRef}
        actionRef={tableRef}
        request={async ({
          current,
          pageSize,
          deptId,
          preDockingUid,
          ...rest
        }) => {
          const { data } = await list91808({
            pageIndex: current,
            pageSize,
            deptId: hasDeptSelect ? deptId?.id : undefined,
            preDockingUid: hasDeptSelect ? preDockingUid?.id : undefined,
            ...rest,
          })
          return { data: data?.rows || [], success: true, total: data?.total }
        }}
        expandable={false}
        pagination={{
          showTotal: (t) => `共${t}条`,
          showSizeChanger: true,
          // showQuickJumper: true,
          defaultPageSize: 10,
        }}
        columns={columns}
        rowKey="id"
        search={{
          className: '[&_.business-ant-pro-query-filter-actions]:hidden',
          collapsed: false,
          collapseRender: false,
          searchGutter: 16,
          resetText: null,
          span: 8,
          optionRender: (_, formProps, dom) => {
            return []
          },
        }}
        toolBarRender={false}
      />
    </div>
  )
}

export default Index
