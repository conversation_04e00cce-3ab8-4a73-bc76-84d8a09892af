/**
 * 接口名称：查询用户是否有全国权限
 * 接口路径：/web-awg/baas-sale/api/auth/check/national
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93674
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetUserAuth93674 {}
export class IResgetUserAuth93674 {
  success?: boolean
  code?: number
  msg?: string
  data?: object[]
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getUserAuth93674: Serve<
  IReqgetUserAuth93674,
  IResgetUserAuth93674
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetUserAuth93674>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/api/auth/check/national',
      data,
      ...options,
    },
    serviceConfig,
  )
