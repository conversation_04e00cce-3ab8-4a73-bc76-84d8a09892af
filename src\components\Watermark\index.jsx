import React, { useEffect, useState } from 'react'
import './style.css'

/**
 * 水印组件
 * @param {Object} props
 * @param {string} props.content - 水印内容
 * @param {string} props.fontColor - 水印字体颜色
 * @param {number} props.fontSize - 水印字体大小
 * @param {number} props.gap - 水印之间的间距
 * @param {number} props.rotate - 水印旋转角度
 * @param {number} props.zIndex - 水印层级
 * @param {number} props.opacity - 水印透明度
 * @param {React.ReactNode} props.children - 子组件
 * @returns {JSX.Element}
 */
const Watermark = ({
  content = '',
  fontColor = 'rgba(0, 0, 0, 0.15)',
  fontSize = 16,
  gap = 100,
  rotate = -22,
  zIndex = 9999,
  opacity = 0.15,
  children,
}) => {
  const [watermarkUrl, setWatermarkUrl] = useState('')

  useEffect(() => {
    if (!content) return

    // 创建离屏Canvas
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    // 设置文本样式
    ctx.font = `${fontSize}px Arial`

    // 计算水印文本的宽度
    const textWidth = ctx.measureText(content).width

    // 设置画布大小，确保能容纳旋转后的文本
    // 对角线长度作为画布大小，确保旋转后文本不会被裁剪
    const diagonalLength =
      Math.ceil(Math.sqrt(textWidth * textWidth + fontSize * fontSize)) + 20
    canvas.width = diagonalLength
    canvas.height = diagonalLength

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 重新设置文本样式（因为canvas尺寸改变后，上下文会重置）
    ctx.fillStyle = fontColor
    ctx.font = `${fontSize}px Arial`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.globalAlpha = opacity

    // 保存当前状态
    ctx.save()

    // 移动到画布中心
    ctx.translate(canvas.width / 2, canvas.height / 2)

    // 旋转画布
    ctx.rotate((rotate * Math.PI) / 180)

    // 绘制文本（居中）
    ctx.fillText(content, 0, 0)

    // 恢复到之前的状态
    ctx.restore()

    // 将Canvas转为DataURL
    const url = canvas.toDataURL('image/png')
    setWatermarkUrl(url)

    return () => {
      // 清理
      canvas.remove()
    }
  }, [content, fontColor, fontSize, gap, rotate, opacity])

  return (
    <div className="watermark-container">
      {watermarkUrl && (
        <div
          className="watermark-layer"
          style={{
            backgroundImage: `url(${watermarkUrl})`,
            backgroundSize: `${gap * 2}px`,
            zIndex,
          }}
        />
      )}
      {children}
    </div>
  )
}

export default Watermark
