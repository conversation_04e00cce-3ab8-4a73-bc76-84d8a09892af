/**
 * 接口名称：需求上报
 * 接口路径：/baas-sale/demand/report
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91806
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqreport91806 {
  /**
   * 线索ID
   */
  clueId: number
  /**
   * 需求上报内容列表
   */
  demandReportContentDTOList?: {
    /**
     * 需求
     */
    demand: string
    /**
     * 业务场景
     */
    businessScene: string
    /**
     * 业务痛点
     */
    businessPain: string
    /**
     * 需求实现时间要求
     */
    achieveDate?: string
    /**
     * 需求提出者身份及角色
     */
    demandProposeStaff?: string
    /**
     * 需求提出者是否具有决策权
     */
    demandProposeDecision?: string
  }[]
  /**
   * 当前登录人userId cookie中获取
   * @NotNull(message = "loginOnUserId不能为空")
   */
  loginOnUserId?: number
  /**
   * 当前登录人所在租户 cookie中获取
   */
  loginOnOrgId?: number
  /**
   * 当前登录人所在租户 cookie中获取
   */
  loginOnUsername?: string
}
export class IResreport91806 {
  code?: number
  msg?: string
  data?: boolean
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const report91806: Serve<IReqreport91806, IResreport91806> = (
  data?,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) =>
  request<IResreport91806>(
    {
      method: 'post',
      url: '/web-awg/baas-sale/demand/report',
      data,
      ...options,
    },
    serviceConfig,
  )
