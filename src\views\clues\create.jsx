import { Button, message, Space, Spin, Typography } from 'antd'
import {
  ProCard,
  ProForm,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components'
import { DatePicker } from '@/components/DatePicker'
import { useHistory } from 'react-router-dom'
import 'antd/es/input/style'
import {
  budgetStatus,
  clueLevel,
  industryOptions,
  projectPhase,
  projectStatus,
} from './constants'
import { options as regionOptions } from '@/assets/city-options/province-new'
import UserSelector from '@/components/UserSelector'
import { useRef } from 'react'
import { getClue91846, saveClue91844 } from '@/apis/project3347'
import REGION_LIST from '@/assets/city-options/list.json'
import Casecader from '@/components/Casecader'
import { useRequest } from 'ahooks'

const { Title } = Typography

const CustomerCreate = () => {
  const history = useHistory()
  const formRef = useRef()
  const searchParams = new URLSearchParams(history.location.search)
  const id = searchParams.get('id')
  const { loading } = useRequest(async () => {
    if (id) {
      const { data } = await getClue91846({
        id,
      })

      const {
        saleDockingUid,
        region,
        saleDockingName,
        preDockingUid,
        preDockingName,
        projectDockingUid,
        projectDockingName,
        ecologyDockingUid,
        ecologyDockingName,
        otherDockingUid,
        otherDockingName,
        ...rest
      } = data
      const detail = {
        ...rest,
        region: data?.region?.split('-'),
        saleDocking: saleDockingUid
          ? {
              id: saleDockingUid,
              name: saleDockingName,
            }
          : null,
        preDocking: preDockingUid
          ? {
              id: preDockingUid,
              name: preDockingName,
            }
          : null,
        projectDocking: projectDockingUid
          ? {
              id: projectDockingUid,
              name: projectDockingName,
            }
          : null,
        ecologyDocking: ecologyDockingUid
          ? {
              id: ecologyDockingUid,
              name: ecologyDockingName,
            }
          : null,
        otherDocking: otherDockingUid
          ? {
              id: otherDockingUid,
              name: otherDockingName,
            }
          : null,
      }

      formRef.current?.setFieldsValue(detail)
    }
  })

  const goBack = () => {
    if (process.env.PUBLIC_H5) {
      window.xm?.close()
    } else {
      history.goBack()
    }
  }

  const { loading: submitLoading, run: onFinish } = useRequest(
    async (values) => {
      const {
        saleDocking,
        preDocking,
        projectDocking,
        ecologyDocking,
        otherDocking,
        region,
        ...rest
      } = values
      const formValue = {
        ...rest,
        id,
        saleDockingUid: saleDocking?.id,
        saleDockingName: saleDocking?.name,
        preDockingUid: preDocking?.id,
        preDockingName: preDocking?.name,
        projectDockingUid: projectDocking?.id,
        projectDockingName: projectDocking?.name,
        ecologyDockingUid: ecologyDocking?.id,
        ecologyDockingName: ecologyDocking?.name,
        otherDockingUid: otherDocking?.id,
        otherDockingName: otherDocking?.name,
        region: region.join('-'),
        regionName: region
          .map((el) => {
            return REGION_LIST[el]
          })
          .join('-'),
      }

      await saveClue91844(formValue)
      message.success('提交成功')
      if (process.env.PUBLIC_H5) {
        window.xm?.close()
      } else {
        history.push('/')
      }
    },
    {
      manual: true,
    },
  )
  return (
    <>
      <Spin spinning={loading}>
        <ProForm
          formRef={formRef}
          onFinish={onFinish}
          initialValues={{
            projectStatus: 0,
          }}
          className={process.env.PUBLIC_H5 ? '' : 'mb-60'}
          scrollToFirstError
          onValuesChange={(values) => {
            const {
              saleDocking,
              preDocking,
              projectDocking,
              ecologyDocking,
              otherDocking,
            } = values

            if (saleDocking) {
              formRef.current?.setFieldsValue({
                saleDockingPhone: saleDocking.mobile,
              })
            }
            if (preDocking) {
              formRef.current?.setFieldsValue({
                preDockingPhone: preDocking.mobile,
              })
            }
            if (projectDocking) {
              formRef.current?.setFieldsValue({
                projectDockingPhone: projectDocking.mobile,
              })
            }
            if (ecologyDocking) {
              formRef.current?.setFieldsValue({
                ecologyDockingPhone: ecologyDocking.mobile,
              })
            }
            if (otherDocking) {
              formRef.current?.setFieldsValue({
                otherDockingPhone: otherDocking.mobile,
              })
            }
          }}
          submitter={false}
        >
          <ProCard title="基本信息" className="mb-16">
            <ProForm.Group>
              <ProFormText
                name="customerCompany"
                label="客户名称"
                width="md"
                placeholder="请输入客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProForm.Item
                name="region"
                rules={[{ required: true, message: '请选择所在区域' }]}
                label="所在区域"
              >
                <Casecader
                  noStyle
                  label="所在区域"
                  fieldProps={{
                    options: regionOptions,
                    changeOnSelect: true,
                  }}
                  width="md"
                  placeholder="请选择所在区域"
                />
              </ProForm.Item>
              <ProFormSelect
                name="industry"
                label="行业"
                width="md"
                options={industryOptions}
                placeholder="请选择行业"
                rules={[{ required: true, message: '请选择行业' }]}
              />
              <ProFormText
                name="projectAmount"
                label="项目金额"
                width="md"
                placeholder="请输入项目金额"
                fieldProps={{
                  maxLength: 100,
                  showCount: true,
                }}
                rules={[{ required: true, message: '请输入项目金额' }]}
              />
              <ProFormSelect
                name="budgetPlace"
                label="预算到位情况"
                width="md"
                options={budgetStatus}
                placeholder="请选择预算到位情况"
                rules={[{ required: true, message: '请选择预算到位情况' }]}
              />
              <ProFormText
                name="fundSource"
                label="资金来源"
                width="md"
                placeholder="请输入资金来源"
                rules={[{ required: true, message: '请输入资金来源' }]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormSelect
                name="projectApproval"
                label="客户是否立项"
                width="md"
                options={projectStatus}
                rules={[{ required: true, message: '请选择客户是否立项' }]}
                placeholder="请选择客户是否立项"
              />
              {/*  预计签单时间 */}
              <ProForm.Item name="expectSigningDate" label="预计签单时间">
                <DatePicker
                  fieldProps={{
                    className: 'pro-field pro-field-md',
                  }}
                  noStyle
                  placeholder="请选择预计签单时间"
                />
              </ProForm.Item>
            </ProForm.Group>
          </ProCard>

          <ProCard title="服务信息" className="mb-16">
            {/*线索等级 A重点项目/B有效商机/C线索/D前期调研*/}
            <ProFormRadio.Group
              radioType="button"
              name="clueLevel"
              label="线索等级"
              width="md"
              fieldProps={{
                className: `flex flex-wrap gap-8 [&_.business-ant-radio-button-wrapper]:rounded-4 [&_.ant-radio-button-wrapper]:rounded-4`,
              }}
              options={clueLevel}
            />
            <ProFormRadio.Group
              radioType="button"
              name="projectStatus"
              label="项目阶段"
              width="md"
              fieldProps={{
                className: `flex flex-wrap gap-8 [&_.business-ant-radio-button-wrapper]:rounded-4 [&_.ant-radio-button-wrapper]:rounded-4`,
              }}
              options={projectPhase.slice(1, projectPhase.length)}
            />
            <ProFormTextArea
              name="projectBackground"
              label="项目背景"
              placeholder="请输入内容"
              rules={[{ required: true, message: '请输入项目背景' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProFormTextArea
              name="progress"
              label="当前进展"
              placeholder="请输入内容"
              rules={[{ required: true, message: '请输入当前进展' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProFormTextArea
              name="demandContent"
              label="具体需求"
              placeholder="请输入内容"
              rules={[{ required: true, message: '请输入具体需求' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProFormTextArea
              name="customerPain"
              label="客户痛点"
              placeholder="请输入内容"
              rules={[{ required: true, message: '请输入客户痛点' }]}
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProForm.Group>
              <ProFormText
                name="demandProposeStaff"
                label="需求提出者身份及角色"
                width="md"
                placeholder="请输入需求提出者身份及角色"
                rules={[
                  { required: true, message: '请输入需求提出者身份及角色' },
                ]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormText
                name="demandProposeDecision"
                label="需求提出者是否具有决策权"
                width="md"
                placeholder="请输入需求提出者是否具有决策权"
                rules={[
                  { required: true, message: '请输入需求提出者是否具有决策权' },
                ]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormText
                name="customerDecisionMaking"
                label="客户决策链"
                width="md"
                placeholder="请输入客户决策链"
                rules={[{ required: true, message: '请输入客户决策链' }]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <ProFormTextArea
              name="shinemoScheme"
              label="讯盟方案及报价"
              placeholder="请输入内容"
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
          </ProCard>

          <ProCard title="生态合作情况" className="mb-16">
            <ProForm.Group>
              <ProFormText
                name="manufacturerName"
                label="厂家名称"
                width="md"
                placeholder="请输入厂家名称"
                rules={[{ required: false, message: '请输入厂家名称' }]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormText
                name="cooperateMode"
                label="合作模式"
                width="md"
                placeholder="请输入合作模式"
                rules={[{ required: false, message: '请输入合作模式' }]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormText
                name="cooperateDemand"
                label="合作需求"
                width="md"
                placeholder="请输入合作需求"
                rules={[{ required: false, message: '请输入合作需求' }]}
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <ProFormText
              name="cooperateProgress"
              label="当前进展"
              width="md"
              placeholder="请输入当前进展"
              rules={[{ required: false, message: '请输入当前进展' }]}
              fieldProps={{
                showCount: true,
                maxLength: 100,
              }}
            />
          </ProCard>

          <ProCard title="竞品信息" className="mb-16">
            <ProFormTextArea
              name="competitors"
              label=""
              placeholder="请输入内容"
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
          </ProCard>

          <ProCard title="项目相关人员" className="mb-16">
            <Title className="text-14" level={5}>
              讯盟对接人
            </Title>
            <ProForm.Group>
              <ProForm.Item
                name="saleDocking"
                label="销售"
                placeholder="请选择"
              >
                <UserSelector title="销售" allowClear />
              </ProForm.Item>

              <ProFormText
                name="saleDockingPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProForm.Item name="preDocking" label="售前" placeholder="请选择">
                <UserSelector title="售前" allowClear />
              </ProForm.Item>
              <ProFormText
                name="preDockingPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProForm.Item
                name="projectDocking"
                label="产品"
                placeholder="请选择"
              >
                <UserSelector title="产品" allowClear />
              </ProForm.Item>
              <ProFormText
                name="projectDockingPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="ecologyDocking"
                label="生态"
                placeholder="请选择"
                width="md"
              />
              <ProFormText
                name="ecologyDockingPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="otherDocking"
                label="其他"
                placeholder="请选择"
                width="md"
              />
              <ProFormText
                name="otherDockingPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <Title className="text-14" level={5}>
              生态对接人
            </Title>
            <ProForm.Group>
              <ProFormText
                name="ecologyName"
                label="生态对接人"
                width="md"
                placeholder="请输入生态对接人"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormText
                name="ecologyPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
            <Title className="text-14" level={5}>
              合作方对接人
            </Title>
            <ProForm.Group>
              <ProFormText
                name="partnerName"
                label="合作方对接人"
                width="md"
                placeholder="请输入合作方对接人"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
              <ProFormText
                name="partnerPhone"
                label="联系方式"
                width="md"
                placeholder="请输入联系方式"
                fieldProps={{
                  showCount: true,
                  maxLength: 100,
                }}
              />
            </ProForm.Group>
          </ProCard>
          <ProCard title="总要求" className="mb-16">
            <ProFormTextArea
              name="notSatisfied"
              label="现有的系统情况，有哪些需求没能满足"
              placeholder="请输入内容"
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
            <ProFormTextArea
              name="demandDiff"
              label="需求实现的难点在于哪些"
              placeholder="此项可咨询客户，用于判断我方实现该需求的可行性，以及分析潜在的风险点"
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
          </ProCard>
          <ProCard title="备注信息" className="mb-16">
            <ProFormTextArea
              name="description"
              label=""
              placeholder="请输入内容"
              fieldProps={{
                showCount: true,
                maxLength: 500,
              }}
            />
          </ProCard>
        </ProForm>
        <div className="page-bottom-button flex bg-#fff w-full px-16 py-14 shadow-[0_1px_0_0_#FFFFFF]">
          <Space>
            <Button
              loading={submitLoading}
              type="primary"
              onClick={() => formRef.current?.submit()}
            >
              提交
            </Button>
            <Button disabled={submitLoading} onClick={goBack}>
              取消
            </Button>
          </Space>
        </div>
      </Spin>
    </>
  )
}

export default CustomerCreate
