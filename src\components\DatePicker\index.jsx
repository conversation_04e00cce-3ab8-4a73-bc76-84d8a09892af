import { ProFormDatePicker, ProFormField } from '@ant-design/pro-components'

export const DatePicker = process.env.PUBLIC_H5
  ? DatePickerMp
  : ProFormDatePicker

function DatePickerMp({ title, label, fieldProps, ...rest }) {
  return (
    <>
      <ProFormField
        noStyle
        label={label}
        width="md"
        placeholder={rest.placeholder}
        fieldProps={{
          ...fieldProps,
          readOnly: true,
          onClick: () => {
            window.xm
              .selectDate({
                format: 'yyyy-MM-dd',
                defaultValue: rest.value
                  ? new Date(rest.value).getTime()
                  : Date.now(),
              })
              .then(function (date) {
                console.log(date) // 2019-12-12 12:12
                // h5 多一层
                rest.onChange(date.data)
              })
          },
        }}
        {...rest}
      />
    </>
  )
}
