import React, { useEffect, useRef, useState } from 'react'
import { <PERSON>vide<PERSON>, InfiniteScroll, List, Popup } from 'antd-mobile'
import {
  CheckthenumberLine,
  DotFill,
  PlusLinearLine,
  ShaixuanLine,
} from '@xm/icons-ai/react-svg'
import { projectPhase as PROJECT_PHASE } from './constants'
// import { sleep } from 'antd-mobile/es/utils/sleep'
import { getClueList91842 } from '@/apis/project3347'
// import { useRequest } from 'ahooks'
import { useHistory } from 'react-router-dom'
// import { session } from '@/utils'
import { useRoleStore } from '@/context/useRoleStore'
import { Tag } from 'antd'

export default () => {
  const history = useHistory()
  const { hasDeptSelect } = useRoleStore()

  const [data, setData] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const [visible, setVisible] = useState(false)
  const pageIndex = useRef(1)

  const [projectStatus, setProjectStatus] = useState(
    sessionStorage.projectStatus ? Number(sessionStorage.projectStatus) : '',
  )
  const [dept, setDept] = useState(
    sessionStorage.dept ? JSON.parse(sessionStorage.dept) : '',
  )

  const loadMore = () =>
    getClueList91842({
      projectStatus,
      deptId: dept?.departmentId,
      pageSize: 10,
      pageIndex: pageIndex.current,
    }).then((_res) => {
      const res = _res.data
      console.log(1, res)
      const _data = [...data, ...(res.list || [])]
      setData(_data)
      setHasMore(res.total > _data.length)
      pageIndex.current += 1
    })

  const reset = () => {
    setData([])
    setHasMore(true)
    pageIndex.current = 1
  }

  useEffect(() => {
    setProjectStatus(
      sessionStorage.projectStatus ? Number(sessionStorage.projectStatus) : '',
    )
    setDept(sessionStorage.dept ? JSON.parse(sessionStorage.dept) : '')
  }, [location.hash])

  return (
    <>
      <List className="[&_.adm-list-body]:bg-transparent [&_.adm-list-item]:bg-transparent [&_.adm-list-item-content]:border-none [&_.adm-list-item-content-main]:pb-0 [&_.adm-list-body]:!border-none pt-44">
        <div
          key={-1}
          className="fixed z-1 w-full top-0 left-0 bg-white h-44 flex items-center overflow-auto text-14 py-7 pl-15 pr-105"
        >
          {PROJECT_PHASE.map((item, index) => (
            <div
              key={index}
              className={`line-height-30 h-30 px-8 ${projectStatus === item.value ? 'c-[var(--xm-primary-color)] bg-[var(--xm-primary-o5-color)]' : 'c-#959BA3 bg-#F7F8F9'} rounded-4 shrink-0 mr-10`}
              onClick={() => {
                sessionStorage.setItem('projectStatus', item.value)
                setProjectStatus(item.value)
                reset()
              }}
            >
              {item.label}
            </div>
          ))}

          <div className="fixed right-0 top-0 h-44 flex items-center">
            <div
              className="w-6 h-full"
              style={{
                backgroundImage:
                  'linear-gradient(90deg, #00000000 0%, #0000000a 100%)',
              }}
            />
            <div
              className="flex items-center justify-center h-full w-95 bg-white"
              style={{
                display: hasDeptSelect ? null : 'none',
              }}
              onClick={() => {
                // setVisible(true)
                window.xm
                  .selectDepartments({
                    orgid: window.Cookies.get('orgId'),
                    count: 1,
                    // selected: deptId
                    //   ? [deptId]
                    //   : [],
                  })
                  .then(function (res) {
                    // [{departmentId: '12313', name: '部门名称'}]
                    const data = res.data
                    console.log(111, data)
                    setDept(data[0])
                    sessionStorage.setItem('dept', JSON.stringify(data[0]))
                    reset()
                  })
              }}
            >
              <span className="c-#5C626B">筛选部门</span>
              <ShaixuanLine size={12} className="inline-flex ml-3" />
            </div>
          </div>
        </div>

        {dept && (
          <Tag
            className="mx-12 mt-12 bg-white text-14"
            bordered={false}
            closable
            onClose={() => {
              sessionStorage.removeItem('dept')
              setDept(null)
              reset()
            }}
          >
            {dept.name}
          </Tag>
        )}

        {data.map((item, index) => (
          <List.Item key={index}>
            <div className="rounded-8 bg-white text-14">
              <div className="p-12">
                <div className="flex justify-between items-center">
                  <div className="text-16 font-500 line-clamp-1 max-w-70%">
                    {item.customerCompany}
                  </div>
                  <div className="flex-shrink-0">
                    <DotFill
                      className="inline-flex mr-8"
                      color={
                        PROJECT_PHASE.find(
                          (x) => x.value === item.projectStatus,
                        )?.color
                      }
                      size={8}
                    />
                    {
                      PROJECT_PHASE.find((x) => x.value === item.projectStatus)
                        ?.label
                    }
                  </div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">地区：</div>
                  <div className="ml-4">{item.regionName}</div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">销售：</div>
                  <div className="ml-4">{item.saleDockingName}</div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">售前：</div>
                  <div className="ml-4">{item.preDockingName}</div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">产品：</div>
                  <div className="ml-4">{item.projectDockingName}</div>
                </div>
              </div>
              <div className="h-44 border-t border-#E9ECF0 mt-12 flex items-center c-[var(--xm-primary-color)]">
                <div
                  className="w-50% h-full flex items-center justify-center"
                  onClick={() => {
                    // history.push(`/clues/detail?id=${item.id}`)
                    const url = new URL(window.location.href)
                    url.hash = `/clues/detail?id=${item.id}`
                    window.xm.openUrl({
                      url: url.toString(),
                      noDefaultMenu: 1,
                    })
                  }}
                >
                  详情
                </div>
                <Divider direction="vertical" className="!h-20" />
                <div
                  className="w-50% h-full flex items-center justify-center"
                  onClick={() => {
                    // history.push(`/clues/create?id=${item.id}`)
                    // openPage('/clues/create', {
                    //   id: item.id,
                    // })
                    const url = new URL(window.location.href)
                    url.hash = `/clues/create?id=${item.id}`
                    window.xm.openUrl({
                      url: url.toString(),
                      noDefaultMenu: 1,
                      closerefresh: 1,
                    })
                  }}
                >
                  编辑
                </div>
              </div>
            </div>
          </List.Item>
        ))}
      </List>
      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />

      <Popup
        visible={visible}
        // showCloseButton
        onClose={() => {
          setVisible(false)
        }}
        onMaskClick={() => {
          setVisible(false)
        }}
        position="top"
        bodyStyle={{
          height: '40vh',
          borderBottomLeftRadius: '8px',
          borderBottomRightRadius: '8px',
        }}
      >
        <div className="px-15 py-10 h-full overflow-auto">
          <div className="h-40 px-15 flex items-center justify-between bg-#f7f8f9 c-[var(--xm-primary-color)] rounded-4">
            <span>全部团队</span>
            <CheckthenumberLine
              color={'var(--xm-primary-color)'}
              className="inline-flex"
            />
          </div>
          <div className="h-40 px-15 flex items-center justify-between rounded-4">
            <span>全部团队</span>
            {/*<RightLine/>*/}
          </div>
        </div>
      </Popup>

      <div
        style={{
          backgroundImage:
            'linear-gradient(138deg, var(--xm-primary-o20-color) 0%, var(--xm-primary-color) 100%)',
        }}
        className="fixed right-15 bottom-108 w-50 h-50 rounded-full flex items-center justify-center"
        onClick={() => {
          // history.push('/clues/create')
          // openPage('/clues/create')
          const url = new URL(window.location.href)
          url.hash = `/clues/create`
          window.xm.openUrl({
            url: url.toString(),
            noDefaultMenu: 1,
            closerefresh: 1,
          })
        }}
      >
        <PlusLinearLine size={18} color="white" />
      </div>
    </>
  )
}
