/**
 * 接口名称：获取用户角色
 * 接口路径：/baas-sale/clue/getUserRole
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91835
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetUserRole91835 {}
export class IResgetUserRole91835 {
  success?: boolean
  code?: number
  msg?: string
  /**
   * 角色：1:销管，2：团队领导，3：政务行业，4：医疗行业，5：K12行业，6：高校行业，7：通用行业
   */
  data?: number
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getUserRole91835: Serve<
  IReqgetUserRole91835,
  IResgetUserRole91835
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetUserRole91835>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/clue/getUserRole',
      data,
      ...options,
    },
    serviceConfig,
  )
