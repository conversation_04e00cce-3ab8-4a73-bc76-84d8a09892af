import React, { useState, useEffect } from 'react'
import { Card, Tag, Tabs } from 'antd'
import { useParams, useHistory } from 'react-router-dom'
import GroupedTable from '@/components/GroupedTable'
import TabFilter from '@/components/TabFilter'
import ProvincialStatsModal from '@/components/ProvincialStatsModal'
import WithWatermark from '../components/WithWatermark'
import {
  getProjectList93656,
  getRegionsList93669,
  getProjectRegionList93653,
} from '@/apis/dataDashboard'
import ICON_CALC from '@/assets/img/dataDashboard/icon_calc.png'
const InactiveRank = () => {
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [modalVisible, setModalVisible] = useState(false)
  const [tabItems, setTabItems] = useState([
    {
      key: '0',
      label: '已部署',
    },
    {
      key: '1',
      label: '部署中',
    },
    {
      key: '2',
      label: '资源下发中',
    },
    {
      key: '3',
      label: '洽谈中',
    },
  ])
  const [activeTab, setActiveTab] = useState('2')
  const [regions, setRegions] = useState([])
  const [provincialStatsData, setProvincialStatsData] = useState([])
  const [totalStats, setTotalStats] = useState({})
  const { id, type } = useParams()
  const [regionId, setRegionId] = useState(id || '')
  const queryFields = [
    {
      label: '区域',
      name: 'regionIds',
      type: 'Cascader',
      options: regions,
    },
  ]
  const tabValues = {
    0: 'toBeDeployed',
    1: 'deployIn',
    2: 'deploy',
    3: 'negotiationNum',
  }
  const tabLabels = {
    0: '资源下发中',
    1: '部署中',
    2: '已部署',
    3: '洽谈中',
  }
  // 表格列配置
  const columns = [
    {
      title: '省份',
      dataIndex: 'provinceName',
      key: 'provinceName',
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '类型',
      dataIndex: 'customerTypeName',
      key: 'customerTypeName',
    },
    {
      title: '模型',
      dataIndex: 'modelVersionName',
      key: 'modelVersionName',
    },
    {
      title: '负责人',
      dataIndex: 'zanshimeiyou',
      key: 'zanshimeiyou',
    },
    {
      title: '报备时间',
      dataIndex: 'zanshimeiyou',
      key: 'zanshimeiyou',
    },
    {
      title: '资源落地计划时间',
      dataIndex: 'zanshimeiyou',
      key: 'zanshimeiyou',
    },
    {
      title: '联系人',
      dataIndex: 'zanshimeiyou',
      key: 'zanshimeiyou',
    },
    {
      title: '联系方式',
      dataIndex: 'zanshimeiyou',
      key: 'zanshimeiyou',
    },
    {
      title: 'GPU类型',
      dataIndex: 'gpuBrandName',
      key: 'gpuBrandName',
    },
    {
      title: '台数',
      dataIndex: 'gpuServerCount',
      key: 'gpuServerCount',
    },
  ]
  // 获取省(市列表) 市(区列表) 统计数据
  const getProjectRegionList = async () => {
    try {
      const { data } = await getProjectRegionList93653({
        regionId: regionId || '0',
      })
      console.log('getProjectRegionList', data)
      const { regionList = [] } = data
      setProvincialStatsData(
        regionList.map((item) => ({
          key: item.provinceId,
          areaName: item.areaName,
          COMPLETED: item.deploy,
          NOT_DEPLOYED: item.deployIn,
          DEPLOYING: item.toBeDeployed,
          NEGOTIATION: item.negotiationNum,
          total:
            item.deploy +
            item.deployIn +
            item.toBeDeployed +
            item.negotiationNum,
        })),
      )
    } catch (error) {
      console.error('获取省(市列表) 市(区列表) 统计数据失败:', error)
    }
  }
  // 平铺的地区数据组装成树状结构
  const buildRegionTree = (regions, parentId = null) => {
    return regions
      .filter((region) => region.parentId === parentId)
      .map((region) => ({
        ...region,
        value: region.id,
        children: buildRegionTree(regions, region.id),
      }))
  }
  const getRegionsList = async () => {
    try {
      const { data } = await getRegionsList93669()
      console.log('getRegions', data)
      // 平铺的地区数据组装成树状结构
      const treeData = buildRegionTree(data, data[0].parentId)
      console.log('treeData', treeData)
      setRegions(treeData)
    } catch (error) {
      console.error('获取区域列表失败:', error)
    }
  }
  const getProjectList = async (params = {}) => {
    setLoading(true)
    const {
      pageIndex = pagination.current,
      pageSize = pagination.pageSize,
      status = activeTab,
      // 使用当前最新的regionId状态值，而不是从URL参数中获取
      customRegionId = regionId,
    } = params

    try {
      const {
        data: { projectList, projectListInfo, total },
      } = await getProjectList93656({
        type: '7',
        status,
        regionId: customRegionId,
        pageIndex,
        pageSize,
      })
      console.log('projectListInfo', projectList, projectListInfo)
      setTabItems((pre) => {
        return pre.map((item) => {
          item.label = `${tabLabels[item.key]}(${projectListInfo[tabValues[item.key]]})`
          return item
        })
      })
      console.log('projectList', projectList)
      setDataSource(projectList)
      setPagination({
        current: pageIndex,
        pageSize,
        total,
      })
    } catch (error) {
      console.error('获取项目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }
  // 监听URL参数变化，更新regionId
  useEffect(() => {
    setRegionId(id || '')
  }, [id])

  useEffect(() => {
    getRegionsList()
  }, [])

  // 当regionId变化时重新获取区域统计数据
  useEffect(() => {
    if (type != '2') {
      getProjectRegionList()
    }
  }, [regionId])
  // 根据选项卡选择获取数据
  useEffect(() => {
    getProjectList({ days: activeTab, pageIndex: 1 })
  }, [activeTab, regionId])
  useEffect(() => {
    setTotalStats({
      COMPLETED: provincialStatsData.reduce(
        (sum, item) => sum + item.COMPLETED,
        0,
      ),
      NOT_DEPLOYED: provincialStatsData.reduce(
        (sum, item) => sum + item.NOT_DEPLOYED,
        0,
      ),
      DEPLOYING: provincialStatsData.reduce(
        (sum, item) => sum + item.DEPLOYING,
        0,
      ),
      NEGOTIATION: provincialStatsData.reduce(
        (sum, item) => sum + item.NEGOTIATION,
        0,
      ),
      total: provincialStatsData.reduce((sum, item) => sum + item.total, 0),
    })
  }, [provincialStatsData])
  // 处理搜索
  const handleSearch = (values) => {
    console.log('搜索条件:', values)
    const { regionIds } = values
    // 4个直辖市code
    const specialCodes = [110000, 120000, 310000, 500000]
    if (regionIds) {
      // 导航到新的URL，regionId会通过useEffect更新
      history.push(
        `/dataDashboard/deploymentAnalysis/${regionIds.length == 3 || (specialCodes.includes(regionIds[0]) && regionIds.length == 2) ? '2' : '1'}/${regionIds[regionIds.length - 1]}`,
      )
    } else {
      // 如果没有选择区域，直接使用当前的查询参数
      getProjectList({
        ...values,
        pageIndex: values.current || pagination.current,
        days: activeTab,
      })
    }
  }
  const HandShakehandsFill = () => {
    setModalVisible(true)
  }
  return (
    <Card className="relative">
      <TabFilter
        className="absolute top-16 left-16"
        items={tabItems}
        activeKey={activeTab}
        onChange={setActiveTab}
      />

      {type != '2' && (
        <div
          className="absolute flex text-#2F7FFF cursor-pointer h-36 line-height-36 top-22 right-360 items-center"
          onClick={HandShakehandsFill}
        >
          <span>区域维度统计</span>
          <img
            style={{ width: '16px', height: '16px', marginLeft: '8px' }}
            src={ICON_CALC}
            alt="区域维度统计"
          />
        </div>
      )}
      <GroupedTable
        columns={columns}
        dataSource={dataSource}
        queryFields={queryFields}
        onSearch={handleSearch}
        pagination={pagination}
        loading={loading}
        fieldAlign="end"
        tableProps={{
          rowKey: 'key',
        }}
      />
      {/* 省级部署统计模态框 */}
      <ProvincialStatsModal
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        provincialStatsData={provincialStatsData}
        totalStats={totalStats}
      />
    </Card>
  )
}

// 使用水印高阶组件包装InactiveRank
export default WithWatermark(InactiveRank)
