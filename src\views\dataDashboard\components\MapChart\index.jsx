import React, { useEffect, useMemo, useRef } from 'react'
import { useRequest } from 'ahooks'
import * as echarts from 'echarts'
import { Button } from 'antd'
import MAP_BG from '@/assets/img/project/map-bg.png'
import { useHistory } from 'react-router-dom'
import <PERSON><PERSON><PERSON> from 'views/dataDashboard/components/PieChart'
import ReactEcharts from 'echarts-for-react'

import { toThousands } from '@/utils'
import {
  CUSTOMER_TYPE,
  GPU_BRAND,
  GPU_CHANNEL,
} from 'views/dataDashboard/constants'

const MapChart = () => {
  const history = useHistory()
  const echartsRef = useRef(null)
  const intervalRef = useRef(null)
  const highlightedIndexRef = useRef({ seriesIndex: -1, dataIndex: -1 })

  const handleRedirect = (state) => {
    history.push('./list', state)
  }

  const {
    data = {
      projectStatusData: [
        {
          name: '已完成',
          color: '#15D888',
          bgColor: 'rgba(21,216,136,0.15)',
        },
        {
          name: '待部署',
          color: '#FFBB33',
          bgColor: 'rgba(255,187,51,0.15)',
        },
        {
          name: '部署中',
          color: '#2277FF',
          bgColor: 'rgba(34,119,255,0.15)',
        },
      ],
    },
  } = useRequest(
    async () => {
      const {
        totalCount,
        provinceCount,
        statusCountMap,
        provinceProjectCountMap = {},
        customerTypeCountMap = {},
        channelCountMap = {},
        gpuTypeMap = {},
      } = {
        totalCount: 0,
        provinceCount: 0,
        statusCountMap: {},
        provinceProjectCountMap: {},
        customerTypeCountMap: {},
        channelCountMap: {},
        gpuTypeMap: {},
      }

      return {
        projectData: {
          total: totalCount,
          regions: provinceCount,
        },
        provinceData: Object.keys(provinceProjectCountMap).map((el) => {
          return {
            name: el,
            value: provinceProjectCountMap[el],
          }
        }),
        industryData: Object.keys(customerTypeCountMap).map((el) => {
          return {
            name: el,
            value: customerTypeCountMap[el],
          }
        }),
        projectStatusData: [
          {
            name: '已完成',
            color: '#15D888',
            bgColor: 'rgba(21,216,136,0.15)',
            value: statusCountMap['已完成'] || 0,
          },
          {
            name: '待部署',
            color: '#FFBB33',
            bgColor: 'rgba(255,187,51,0.15)',
            value: statusCountMap['未部署'] || 0,
          },
          {
            name: '部署中',
            color: '#2277FF',
            bgColor: 'rgba(34,119,255,0.15)',
            value: statusCountMap['部署中'] || 0,
          },
        ],
        channelData: Object.keys(channelCountMap).map((el) => {
          return {
            name: el,
            value: channelCountMap[el],
          }
        }),
        // gpuTypeMap {
        //   "华为": {
        //     "300i": 1,
        //     "910C": 1
        //   }
        // }
        gpuTypeData: Object.keys(gpuTypeMap).map((el) => {
          return {
            name: el,
            value: Object.values(gpuTypeMap[el]).reduce((acc, cur) => {
              return acc + cur
            }, 0),
          }
        }),
      }
    },
    {
      pollingInterval: 1000 * 60,
    },
  )
  const {
    projectData = {},
    provinceData = [],
    industryData = [],
    projectStatusData = [],
    channelData = [],
    gpuTypeData = [],
  } = data

  const { data: scatterData = [] } = useRequest(
    async () => {
      const data = {
        list: [
          {
            projectName: '项目1',
            longLat: [112.397428, 32.90923],
            status: 'COMPLETED',
          },
          {
            projectName: '项目2',
            longLat: [113.465302, 35.90923],
            status: 'NOT_DEPLOYED',
          },
          {
            projectName: '项目3',
            longLat: [118.465302, 37.90923],
            status: 'DEPLOYING',
          },
          {
            projectName: '项目4',
            longLat: [116.465302, 39.90923],
            status: 'DEPLOYING',
          },
        ],
      }

      return data?.list?.map((el) => {
        return {
          name: el?.projectName,
          value: el?.longLat,
          status: el?.status,
        }
      })
    },
    {
      pollingInterval: 1000 * 60,
    },
  )
  // 加载中国地图数据
  const { loading = true } = useRequest(async () => {
    try {
      const response = await fetch(
        'https://ai-paas-ytj.uban360.com/statics/cdn/json/china.json',
      )
      const json = await response.json()
      echarts.registerMap('china', json)
    } catch (error) {
      console.error('Failed to load China map data:', error)
    }
  })

  const mapOption = useMemo(() => {
    if (loading) return {}

    const areaItemStyle = {
      // 地图区域的多边形 图形样式
      normal: {
        borderColor: '#BDDDF7',
        borderWidth: 0.75,
        areaColor: '#F7FEFF', // 地图图形颜色
      },
      emphasis: {
        areaColor: '#E7F8FF',
        borderColor: '#BDDDF7',
        borderWidth: 0.75,
      },
    }

    const mapData = provinceData.map((item) => ({
      name: item.name,
      value: item.value,
      itemStyle: {
        borderColor: '#BDDDF7',
        borderWidth: 0.75,
        areaColor: '#E7F8FF', // 地图图形颜色
      },
    }))

    return {
      backgroundColor: 'transparent',
      tooltip: {
        className:
          'hidden !p-6 !border-none [&>div]:!border-#fff shadow-[0_0_10px_0_#0000001a] rounded-2 !text-12',
        position: 'top',
        triggerOn: 'none',
        formatter: function (params) {
          // if (params.seriesType === 'map') {
          //   return `${params.name}<br/>项目数量：${params.value || 0}`
          // } else
          if (params.seriesType === 'effectScatter') {
            const typeMap = {
              COMPLETED: projectStatusData[0],
              NOT_DEPLOYED: projectStatusData[1],
              DEPLOYING: projectStatusData[2],
            }
            return `<div class="flex items-center">
            <div class="py-2 px-4 rounded-4 mr-4"
            style="color: ${
              typeMap[params.data.status]?.color
            };background-color: ${
              typeMap[params.data.status]?.bgColor
            }">${typeMap[params.data.status]?.name}</div>${params.name}</div>`
          }
        },
      },
      geo: [
        {
          map: 'china',
          aspectScale: 0.8,
          zlevel: -1,
          zoom: 1.23,
          layoutCenter: ['50%', '50%'],
          // 如果宽高比大于 1 则宽度为 100，如果小于 1 则高度为 100，保证了不超过 100x100 的区域
          layoutSize: '100%',
          roam: false,
          label: {
            show: false, // 各个省市县的名字
          },
          emphasis: {
            disabled: true,
          },
          itemStyle: areaItemStyle,
        },
        {
          map: 'china',
          aspectScale: 0.8,
          zlevel: -2,
          zoom: 1.23,
          layoutCenter: ['50.5%', '50.8%'],
          // 如果宽高比大于 1 则宽度为 100，如果小于 1 则高度为 100，保证了不超过 100x100 的区域
          layoutSize: '100%',
          roam: false,
          label: {
            show: false, // 各个省市县的名字
          },
          tooltip: {
            show: false,
          },
          emphasis: {
            disabled: true,
          },
          itemStyle: {
            borderColor: 'rgba(0, 142, 255, 0.3)',
            borderWidth: 1,
            areaColor: '#D9DDE4',
            shadowColor: 'rgba(0, 142, 255, 0.4)',
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
          },
          // 隐藏南海诸岛
          regions: [
            {
              name: '南海诸岛',
              itemStyle: {
                normal: {
                  areaColor: 'rgba(0, 0, 0, 0)',
                  borderColor: 'rgba(0, 0, 0, 0)',
                },
                emphasis: {
                  areaColor: 'rgba(0, 0, 0, 0)',
                  borderColor: 'rgba(0, 0, 0, 0)',
                },
              },
            },
          ],
        },
      ],
      series: [
        {
          name: '全国地图',
          type: 'map',
          mapType: 'china',
          layoutCenter: ['50%', '50%'],
          layoutSize: '100%',
          aspectScale: 0.8, // 长宽比
          roam: false, // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 `'scale'` 或者 `'move'`。设置成 `true` 为都开启
          zoom: 1.23, // 当前视角的缩放比例,
          data: mapData, // Add province data for highlighting
          label: {
            show: false,
          },
          selectedMode: false, // 选中模式，默认关闭
          emphasis: {
            label: {
              show: false,
            },
          },
          // select: {
          //   itemStyle: {
          //     areaColor: 'rgba(21, 185, 237, 0.6)',
          //     borderColor: '#56BDFF',
          //     borderWidth: 2
          //   },
          //   label: {
          //     show: false
          //   }
          // },
          itemStyle: areaItemStyle,
        },
        {
          name: '已完成',
          type: 'effectScatter',
          coordinateSystem: 'geo',
          showEffectOn: 'emphasis',
          data: scatterData.filter((item) => item.status === 'COMPLETED'),
          symbolSize: 12,
          rippleEffect: {
            color: 'rgba(21,216,136,0.4)',
            borderColor: '#fff',
            borderWidth: 3,
            brushType: 'fill',
            period: 2,
            scale: 4,
          },
          itemStyle: {
            normal: {
              color: '#15D888',
              // 增加一圈白色边框
              borderColor: '#fff',
              borderWidth: 3,
            },
          },
          emphasis: {},
        },
        {
          name: '待部署',
          type: 'effectScatter',
          coordinateSystem: 'geo',
          showEffectOn: 'emphasis',
          data: scatterData.filter((item) => item.status === 'NOT_DEPLOYED'),
          symbolSize: 12,
          label: {
            normal: {
              show: false,
            },
            emphasis: {
              show: false,
            },
          },
          rippleEffect: {
            color: 'rgba(255,187,51,0.4)',
            brushType: 'fill',
            period: 2,
            scale: 4,
          },
          itemStyle: {
            normal: {
              color: '#FFBB33',
            },
          },
        },
        {
          name: '部署中',
          type: 'effectScatter',
          showEffectOn: 'emphasis',
          coordinateSystem: 'geo',
          data: scatterData.filter((item) => item.status === 'DEPLOYING'),
          symbolSize: 12,
          label: {
            normal: {
              show: false,
            },
            emphasis: {
              show: false,
            },
          },
          rippleEffect: {
            color: 'rgba(34,119,255,0.4)',
            brushType: 'fill',
            period: 2,
            scale: 4,
          },
          itemStyle: {
            normal: {
              color: '#2277FF',
            },
          },
        },
      ],
    }
  }, [scatterData, loading, provinceData]) // Add provinceData as dependency

  const clearHighlight = () => {
    const echartsInstance = echartsRef.current?.getEchartsInstance()
    if (echartsInstance && highlightedIndexRef.current.seriesIndex !== -1) {
      echartsInstance.dispatchAction({
        type: 'downplay',
        seriesIndex: highlightedIndexRef.current.seriesIndex,
        dataIndex: highlightedIndexRef.current.dataIndex,
      })
      highlightedIndexRef.current = { seriesIndex: -1, dataIndex: -1 }
    }
  }

  const startRandomHighlight = () => {
    clearInterval(intervalRef.current)
    intervalRef.current = setInterval(() => {
      const echartsInstance = echartsRef.current?.getEchartsInstance()
      if (
        !echartsInstance ||
        loading ||
        !scatterData ||
        scatterData.length === 0
      )
        return

      clearHighlight()

      // Filter series with data
      const availableSeries = mapOption.series
        .map((s, index) => ({ ...s, originalIndex: index }))
        .filter(
          (s) => s.type === 'effectScatter' && s.data && s.data.length > 0,
        )

      if (availableSeries.length === 0) return

      // Select a random series
      const randomSeriesIndex = Math.floor(
        Math.random() * availableSeries.length,
      )
      const selectedSeries = availableSeries[randomSeriesIndex]

      // Select a random data point within the series
      const randomDataIndex = Math.floor(
        Math.random() * selectedSeries.data.length,
      )
      echartsInstance.dispatchAction({
        type: 'highlight',
        seriesIndex: selectedSeries.originalIndex,
        dataIndex: randomDataIndex,
      })
      // Add this line to show tooltip
      echartsInstance.dispatchAction({
        type: 'showTip',
        seriesIndex: selectedSeries.originalIndex,
        dataIndex: randomDataIndex,
      })

      highlightedIndexRef.current = {
        seriesIndex: selectedSeries.originalIndex,
        dataIndex: randomDataIndex,
      }
    }, 2000) // Highlight every 2 seconds
  }

  useEffect(() => {
    if (!loading && echartsRef.current) {
      startRandomHighlight()
    }
    return () => {
      clearInterval(intervalRef.current)
      clearHighlight() // Clear highlight on unmount
    }
  }, [loading, scatterData, mapOption]) // Re-run effect if loading state or data changes

  const onEvents = {
    mouseover: (params) => {
      if (
        params.componentType === 'series' &&
        params.seriesType === 'effectScatter'
      ) {
        const echartsInstance = echartsRef.current?.getEchartsInstance()
        if (!echartsInstance) return

        clearInterval(intervalRef.current)
        clearHighlight() // Clear random highlight

        // Show tooltip for hovered item
        echartsInstance.dispatchAction({
          type: 'showTip',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })

        // Highlight hovered item
        echartsInstance.dispatchAction({
          type: 'highlight',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })

        highlightedIndexRef.current = {
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        }
      }
    },
    mouseout: (params) => {
      if (
        params.componentType === 'series' &&
        params.seriesType === 'effectScatter'
      ) {
        const echartsInstance = echartsRef.current?.getEchartsInstance()
        if (!echartsInstance) return

        // Clear hover highlight
        echartsInstance.dispatchAction({
          type: 'downplay',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })

        // Hide tooltip
        echartsInstance.dispatchAction({
          type: 'hideTip',
        })

        // Restart random highlights
        startRandomHighlight()
      }
    },
  }

  return (
    <div className="relative w-full h-full pt-80 mt-13 overflow-hidden bg-white">
      <img
        className="absolute z-0 w-900 h-900 left-50% top-55% -translate-x-1/2 -translate-y-1/2"
        src={MAP_BG}
        alt=""
      />
      <div className="absolute z-3 top-40 left-56">
        <div className="flex gap-80">
          <div className="flex-col gap-12">
            <div className="text-#262A30 text-16 font-bold">项目总计</div>
            <div className="leading-[1] text-56 font-bold text-#2277FF">
              {projectData.total !== undefined
                ? toThousands(projectData.total)
                : '-'}
            </div>
          </div>
          {/*<div className="flex-col gap-12">*/}
          {/*  <div className="text-#262A30 text-16 font-bold">租户总计</div>*/}
          {/*  <div className="leading-[1] text-56 font-bold text-#2277FF">*/}
          {/*    {projectData.tenants}*/}
          {/*  </div>*/}
          {/*</div>*/}
        </div>
        <div className="flex-col gap-16 mt-40">
          {projectStatusData.map((item) => (
            <div key={item.name} className="flex items-center mr-6">
              <div
                className="w-10 h-10 rounded-full mr-10"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-16 text-#262A30">{item.name}</span>
              <span className="text-16 text-#3A3A3A ml-30">{item.value}</span>
            </div>
          ))}
        </div>

        <Button
          type="primary"
          className="mt-24 w-120"
          onClick={() => {
            history.push('./list')
          }}
        >
          项目管理
        </Button>
      </div>
      <div className="absolute z-3 left-56 bottom-40 flex">
        <div className="flex-col mr-48">
          <div className="font-bold text-14 text-#262A30">项目渠道</div>
          <PieChart
            handleClick={(name) => {
              handleRedirect({
                channel: GPU_CHANNEL?.find((_item) => _item.label === name)
                  ?.value,
              })
            }}
            data={channelData}
          />
        </div>
        <div className="flex-col">
          <div className="font-bold text-14 text-#262A30">算力资源类型</div>
          <PieChart
            handleClick={(name) => {
              handleRedirect({
                gpuBrand: GPU_BRAND?.find((_item) => _item.label === name)
                  ?.value,
              })
            }}
            data={gpuTypeData}
          />
        </div>
      </div>
      <div className="absolute z-3 top-40 right-40">
        <div className="flex-col gap-12">
          <div className="text-#262A30 text-16 font-bold">覆盖省份</div>
          <div className="leading-[1] text-56 font-bold text-#2277FF">
            {projectData?.regions || 0}
          </div>
        </div>
        <div className="text-14 text-#262A30 font-bold mt-24 leading-[1.2]">
          各省份情况
        </div>
        <ul className="mt-16 flex-col gap-12">
          {provinceData.map((item) => (
            <li
              key={item.name}
              onClick={() => {
                handleRedirect({
                  province: item.name,
                })
              }}
              className="flex justify-between leading-[1.2] gap-4 w-100 text-12 text-#262A30 cursor-pointer"
            >
              <span className="font-bold basis-[50%] line-clamp-1">
                {item.name}
              </span>
              <span className="font-bold basis-[50%] line-clamp-1">
                {item.value}
              </span>
              {/*<span className="basis-[33%] line-clamp-1">{item.unit}</span>*/}
            </li>
          ))}
        </ul>

        <div className="text-14 text-#262A30 font-bold mt-40 leading-[1.2]">
          各行业情况
        </div>
        <ul className="mt-16 flex-col gap-12">
          {industryData.map((item) => (
            <li
              key={item.name}
              onClick={() => {
                handleRedirect({
                  industry: CUSTOMER_TYPE?.find(
                    (_item) => _item.label === item.name,
                  )?.value,
                })
              }}
              className="flex justify-between leading-[1.2] gap-4 w-100 text-12 text-#262A30 cursor-pointer"
            >
              <span className="font-bold basis-[50%] line-clamp-1">
                {item.name}
              </span>
              <span className="font-bold basis-[50%] line-clamp-1">
                {item.value}
              </span>
              {/*<span className="basis-[33%] line-clamp-1">{item.unit}</span>*/}
            </li>
          ))}
        </ul>
      </div>

      <ReactEcharts
        ref={echartsRef}
        loading={loading}
        option={mapOption}
        className="relative z-2 w-full !h-560"
        onEvents={onEvents} // Add event listeners
      />
      {/*<div ref={chartRef} className="relative z-2 w-full h-560"/>*/}
    </div>
  )
}

export default MapChart
