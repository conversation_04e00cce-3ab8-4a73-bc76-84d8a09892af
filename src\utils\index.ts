export const getLabelByValue = (
  value: string | number,
  options: [{ label: string; value: string | number }],
) => {
  return (
    options.find((option) => option.value !== '' && +option.value === +value)
      ?.label || ''
  )
}

export const isPc = () => !!window.SHMJSBridge

export function toThousands(amount: string | number | undefined) {
  if (amount === undefined) return '-'
  const num = Number(amount)
  if (isNaN(num)) return '-'

  // 核心判断逻辑
  if (num >= 10000) {
    const wValue = num / 10000
    // 千分位，并且保留两位小数
    return `${wValue.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}W`
  }
  // 千级以下数值：常规千分位
  return num.toLocaleString('en-US')
}
