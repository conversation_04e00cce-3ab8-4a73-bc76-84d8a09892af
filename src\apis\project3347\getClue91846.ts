/**
 * 接口名称：获取线索详情
 * 接口路径：/baas-sale/clue/getClue
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91846
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetClue91846 {
  id: string | number
}
export class IResgetClue91846 {
  success?: boolean
  code?: number
  msg?: string
  data?: {
    id?: number
    /**
     * 客户单位名称
     */
    customerCompany?: string
    /**
     * 所属地区
     */
    region?: string
    /**
     * 所属地区，省级-市级-县级
     */
    regionName?: string
    /**
     * 行业,0:通用，1：政务，2：医疗，3：K12，4：高校
     */
    industry?: number
    /**
     * 项目金额
     */
    projectAmount?: string
    /**
     * 预算到位情况，0:无，1：申请中，2：已到位
     */
    budgetPlace?: number
    /**
     * 资源来源
     */
    fundSource?: string
    /**
     * 客户是否立项，0:未立项，1：申请中，2：已立项
     */
    projectApproval?: number
    /**
     * 预计签单时间
     */
    expectSigningDate?: string
    /**
     * 线索等级，1:重点项目，2：有效商机，3：线索，4：前期调研
     */
    clueLevel?: number
    /**
     * 项目状态，0:初步建联，1：POC阶段，2：商务阶段，3：已签单，4：搁置，5：关闭
     */
    projectStatus?: number
    /**
     * 项目背景
     */
    projectBackground?: string
    /**
     * 当前进展
     */
    progress?: string
    /**
     * 具体需求
     */
    demandContent?: string
    /**
     * 客户痛点
     */
    customerPain?: string
    /**
     * 需求提出者身份及角色
     */
    demandProposeStaff?: string
    /**
     * 需求提出者是否具有决策权
     */
    demandProposeDecision?: string
    /**
     * 客户决策链
     */
    customerDecisionMaking?: string
    /**
     * 讯盟方案及提价
     */
    shinemoScheme?: string
    /**
     * 厂家名称
     */
    manufacturerName?: string
    /**
     * 合作模式
     */
    cooperateMode?: string
    /**
     * 合作需求
     */
    cooperateDemand?: string
    /**
     * 合作当前进展
     */
    cooperateProgress?: string
    /**
     * 竞品信息
     */
    competitors?: string
    /**
     * 备注
     */
    description?: string
    /**
     * AI总结标题
     */
    aiTitle?: string
    /**
     * AI总结
     */
    aiSummarize?: string
    /**
     * 创建人
     */
    createrUid?: number
    /**
     * 创建人名称
     */
    createrName?: string
    /**
     * 销售对接人UID
     */
    saleDockingUid?: number
    /**
     * 销售对接人姓名
     */
    saleDockingName?: string
    /**
     * 销售对接人联系方式
     */
    saleDockingPhone?: string
    /**
     * 售前对接人UID
     */
    preDockingUid?: number
    /**
     * 售前对接人姓名
     */
    preDockingName?: string
    /**
     * 售前对接人联系方式
     */
    preDockingPhone?: string
    /**
     * 产品对接人UID
     */
    projectDockingUid?: number
    /**
     * 产品对接人姓名
     */
    projectDockingName?: string
    /**
     * 产品对接人联系方式
     */
    projectDockingPhone?: string
    /**
     * 生态对接人UID
     */
    ecologyDockingUid?: number
    /**
     * 生态对接人姓名
     */
    ecologyDockingName?: string
    /**
     * 生态对接人联系方式
     */
    ecologyDockingPhone?: string
    /**
     * 其他对接联系人
     */
    otherDockingUid?: number
    /**
     * 其他对接联系人姓名
     */
    otherDockingName?: string
    /**
     * 其他对接联系人联系方式
     */
    otherDockingPhone?: string
    /**
     * 生态侧联系人
     */
    ecologyName?: string
    /**
     * 生态侧联系方式
     */
    ecologyPhone?: string
    /**
     * 合作方联系人
     */
    partnerName?: string
    /**
     * 合作方联系方式
     */
    partnerPhone?: string
    /**
     * 关联需求列表
     */
    demandList?: {
      id?: number
      /**
       * 需求标题
       */
      title?: string
    }[]
    commentList?: {
      id?: number
      uid?: number
      /**
       * 姓名
       */
      name?: string
      /**
       * 评论内容
       */
      content?: string
      /**
       * 创建时间
       */
      gmtCreate?: string
    }[]
  }
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getClue91846: Serve<IReqgetClue91846, IResgetClue91846> = (
  data?,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) =>
  request<IResgetClue91846>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/clue/getClue',
      data,
      ...options,
    },
    serviceConfig,
  )
