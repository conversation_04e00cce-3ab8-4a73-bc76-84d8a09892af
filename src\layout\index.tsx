import {
  <PERSON><PERSON><PERSON><PERSON>,
  ProCard,
  ProConfigProvider,
  ProLayout,
} from '@ant-design/pro-components'
import { Button, ConfigProvider, LayoutProps } from 'antd'
import React, { useEffect, useState } from 'react'
import defaultProps from './_defaultProps.tsx'
import {
  legacyLogicalPropertiesTransformer,
  StyleProvider,
} from '@ant-design/cssinjs'
import zhCN from 'antd/locale/zh_CN'
import logo from './logo.png'
import XmIconReact from '@xm/icons-ai/react-svg'
import { useHistory } from 'react-router-dom'
import chatIcon from '@/assets/img/chat.png'
// @ts-ignore
import routerConfig from '@/router'
import { useGlobalState } from '@xm/xapp-main'
import { isPc } from 'utils'
// @ts-ignore
import { getFullLocationName } from '@/utils/locationUtils'

interface ILayoutProps extends LayoutProps {
  children: React.ReactNode
}

function Wrapper({ children }: { children: JSX.Element }) {
  const chromeVersion = navigator.userAgent.match(/chrome\/(\d+)/i)
  const isLowerChrome = chromeVersion ? Number(chromeVersion[1]) < 89 : false
  window.isLowerChrome = isLowerChrome

  return isLowerChrome ? (
    <StyleProvider
      hashPriority="high"
      transformers={[legacyLogicalPropertiesTransformer]}
    >
      {children}
    </StyleProvider>
  ) : (
    children
  )
}

export default (props: ILayoutProps) => {
  const [pathname, setPathname] = useState('')
  const [query, setQuery] = useState({} as { id: string | null })
  const history = useHistory()
  const globalState = useGlobalState()

  useEffect(() => {
    const _path = location.hash
      .split('?')[0]
      .split('/')
      .filter(Boolean)
      .slice(3)
      .join('/')
    setPathname(`/${_path}`)

    const _query = location.hash.split('?')[1]
    if (_query) {
      const query = new URLSearchParams(_query)
      setQuery({
        id: query.get('id'),
      })
    } else {
      setQuery({
        id: null,
      })
    }
  }, [location.hash])

  if (typeof document === 'undefined') {
    return <div />
  }
  return (
    <div
      id="test-pro-layout"
      style={{
        transform: 'translateZ(0)',
      }}
      className="h-full overflow-hidden"
    >
      <ProConfigProvider hashed={false}>
        {/*<Wrapper>*/}
        <ConfigProvider
          locale={zhCN}
          prefixCls="business-ant"
          getTargetContainer={() => {
            return document.getElementById('test-pro-layout') || document.body
          }}
          theme={{ token: { colorPrimary: '#FF9222' } }}
        >
          <ProLayout
            title=""
            logo={logo}
            prefixCls="my-prefix"
            {...defaultProps}
            location={{
              pathname,
            }}
            token={{
              header: {
                colorBgHeader: '#fff',
                // colorBgMenuItemSelected: 'rgba(0,0,0,0.04)',
              },
              pageContainer: {
                paddingBlockPageContainerContent: 0,
              },
            }}
            siderMenuType="group"
            menu={{
              collapsedShowGroupTitle: true,
            }}
            actionsRender={(props) => {
              if (props.isMobile) return []
              if (typeof window === 'undefined') return []
              return [
                <Button
                  type="default"
                  className="mr-4 border-0 shadow-none bg-#F7F8F9"
                  onClick={() => {
                    location.hash = '#/portal/business-sale/chat'
                  }}
                >
                  <img src={chatIcon} width={18} alt="" />
                  <span
                    style={{
                      // 字体颜色线性渐变
                      background:
                        'linear-gradient(90deg, #3087FF 0%, #F389FF 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                    }}
                  >
                    智能问答
                  </span>
                </Button>,
                <Button
                  type="primary"
                  className="gap-4 line-height-normal"
                  icon={
                    <XmIconReact
                      className="!flex"
                      color="#fff"
                      size={16}
                      name="plus_linear_line"
                    />
                  }
                  onClick={() => {
                    location.hash = '#/portal/business-sale/clues/create'
                  }}
                >
                  上报线索
                </Button>,
                // props.layout !== 'side' && document.body.clientWidth > 1400 ? (
                //   <SearchInput />
                // ) : undefined,
                // <InfoCircleFilled key="InfoCircleFilled" />,
                // <QuestionCircleFilled key="QuestionCircleFilled" />,
                // <GithubFilled key="GithubFilled" />,
              ]
            }}
            headerTitleRender={(logo, title, _) => {
              const defaultDom = (
                <a>
                  {logo}
                  {title}
                </a>
              )
              if (typeof window === 'undefined') return defaultDom
              if (document.body.clientWidth < 1400) {
                return defaultDom
              }
              if (_.isMobile) return defaultDom
              return <>{defaultDom}</>
            }}
            onMenuHeaderClick={(e) => console.log(e)}
            breadcrumbRender={(routes) => {
              console.log('======routes========', routes)
              const append = []
              const arr = routerConfig[0].children
              const item = arr.find(
                (item: {
                  path: string
                  meta: { title: string; title2: string; title3: string }
                }) => {
                  if (item.path.includes(':')) {
                    const pathPattern = item.path
                      .replace(/:[^/]+/g, '[^/]+')
                      .replace(/\//g, '\\/')

                    const regex = new RegExp(`^${pathPattern}$`)
                    return regex.test(pathname)
                  }

                  return item.path === pathname
                },
              )
              const meta = item?.meta
              if (meta?.title) {
                let breadcrumbName = meta.title

                if (
                  item?.path.includes('deploymentOverview') ||
                  item?.path.includes('deploymentAnalysis') ||
                  item?.path.includes('newDeployment') ||
                  item?.path.includes('deploymentSite') ||
                  item?.path.includes('deploymentIndustry')
                ) {
                  const match = pathname.match(/\/(\w+)\/(\w+)$/)
                  console.log('======match========', match)
                  if (match) {
                    const id = item?.path.includes('deploymentSite')
                      ? match[1]
                      : match[2]
                    console.log('======id========', id)
                    // 根据id获取区域完整名称
                    const locationName = getFullLocationName(id)

                    breadcrumbName = locationName
                      ? `${locationName}${meta.title}`
                      : meta.title
                  }
                } else if (query.id) {
                  breadcrumbName = meta.title2 || meta.title
                }

                append.push({
                  breadcrumbName,
                  linkPath: item?.path,
                })
              }
              return routes
                ?.map((route) => {
                  // @ts-ignore
                  route.linkPath = `/portal/#/portal/business-sale${route.linkPath}`
                  return route
                })
                .concat(append)
            }}
            breadcrumbProps={{
              separator: (
                <XmIconReact
                  className="inline-flex mt-4"
                  size={14}
                  name="right_line"
                />
              ),
              // itemRender: (_, route, _, paths) => {
              //   console.log(111, route)
              //   return (
              //     <a
              //       href={route.linkPath}
              //       target="_blank"
              //       rel="noopener noreferrer"
              //     >
              //       {route.breadcrumbName}
              //     </a>
              //   )
              // }
            }}
            menuItemRender={(item, dom) => {
              return (
                <div
                  onClick={() => {
                    if (item.path === '/doc') {
                      const path =
                        location.origin +
                        `/portal/#/portal/web-clouddisk/newCloudDriver/company/${globalState.loginInfo.orgId}/4/0/0`
                      if (isPc()) {
                        const { JSSDK } = window.WEB_UTIL || {}
                        JSSDK?.call('system', 'openNewTab', {
                          url: path,
                          title: '云盘',
                          iconPath: '',
                          parameter: undefined,
                        })
                      } else {
                        window.open(path)
                      }
                      return
                    }
                    setPathname(item.path || '/')
                    history.push(item.path || '/')
                  }}
                >
                  {dom}
                </div>
              )
            }}
          >
            <PageContainer
              token={{
                paddingInlinePageContainerContent: 0,
              }}
              // extra={[
              //   <Button key="3">操作</Button>,
              //   <Button key="2">操作</Button>,
              //   <Button
              //     key="1"
              //     type="primary"
              //     onClick={() => {
              //       setNum(num > 0 ? 0 : 40)
              //     }}
              //   >
              //     主操作
              //   </Button>,
              // ]}
              subTitle=""
              // footer={[
              //   <Button key="3">重置</Button>,
              //   <Button key="2" type="primary">
              //     提交
              //   </Button>,
              // ]}
            >
              <ProCard
                style={
                  {
                    // height: '200vh',
                    // minHeight: 800,
                    // height: 'calc(100vh - 60px - 55px - 25px)',
                    // overflow: 'auto',
                  }
                }
                bodyStyle={{
                  paddingTop: 0,
                  marginTop: 16,
                  overflow: 'auto',
                }}
                bordered={false}
                className="bg-[#F7F8F9]"
              >
                {props.children}
              </ProCard>
            </PageContainer>
          </ProLayout>
        </ConfigProvider>
        {/*</Wrapper>*/}
      </ProConfigProvider>
    </div>
  )
}
