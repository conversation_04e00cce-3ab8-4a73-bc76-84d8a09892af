import type { AttributifyAttributes } from 'unocss/preset-attributify'

declare module 'react' {
  interface HTMLAttributes<T> extends AttributifyAttributes {}
}

declare global {
  interface Window {
    // 这个方法只有在保存和下载的时候需要
    // _emitPreviewLoadResolve?: Promise.resolve<HTMLDivElement>
    _emitPreviewLoadResolve?: (ref: HTMLDivElement | null) => void
    _APP_CONFIG: {
      generateImagesHost: string
    }

    __cacheScrollTop?: number

    __clickTools?: boolean
    // 避免两次点击，消费手动触发click事件
    __clickHandler?: () => void
    // 解决useClickAway点击富文本区域不生效的问题
    __clickQuestionArea?: () => void
    __setShowPopConfirmPanel?: (open: boolean) => void

    isLowerChrome?: boolean

    __tinymce: {
      init: (
        selector: string | HTMLDivElement,
        options?: {
          onInit?: () => void
          onNodeChange?: () => void
          onContentChange?: (content: string, editor: any) => void
          onSetup?: (editor: any) => () => void
          toolbar?: string
          uploadUrl?: string
          config?: {
            [k: string]: any
          }
        },
      ) => Promise<any>
      getGroupBlankLine: (
        rows: { qsNumber?: string; lineLen: number }[][],
        type: 'solid-line' | 'dashed-line',
      ) => string
      SOLID: 'solid-line'
      DASHED: 'dashed-line'
    }

    __businessCheckImageDimensions: (
      image: {
        dimensions: { width: number; height: number }
        width: number
        height: number
      },
      editor: any,
    ) => { success: boolean; msg?: string }

    WEB_UTIL: {
      JSSDK: any
    }
  }
}
