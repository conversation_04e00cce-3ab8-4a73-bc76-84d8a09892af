/**
 * 接口名称：获取地区列表
 * 接口路径：/web-awg/baas-sale/api/auth/user/getByUserId
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93669
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetRegionsList93669 {}
export class IResgetRegionsList93669 {
  success?: boolean
  code?: number
  msg?: string
  data?: object[]
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getRegionsList93669: Serve<
  IReqgetRegionsList93669,
  IResgetRegionsList93669
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetRegionsList93669>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/api/auth/user/getByUserId',
      data,
      ...options,
    },
    serviceConfig,
  )
