import { <PERSON><PERSON>, Card, Mentions } from 'antd'
import XmIconReact from '@xm/icons-ai/react-svg'
import { useAppContext } from '@xm/xapp-main'
import { useBoolean, useRequest } from 'ahooks'
import { addComment91881, getAllUser91847 } from '@/apis/project3347'
import { useRef, useState } from 'react'
import { XAvatar } from '@xm/xpro-react'

// 自定义评论组件（保持不变）
const CommentItem = ({ uid, name, gmtCreate, content, isMe, options }) => {
  const regex = /@([^@\s]+)/g

  const renderContent = (text) => {
    const parts = []
    let lastIndex = 0
    let match

    while ((match = regex.exec(text)) !== null) {
      // 添加@之前的文本
      if (match.index > lastIndex) {
        parts.push(text.slice(lastIndex, match.index))
      }
      if (options?.find((opt) => opt.value === match[1])) {
        // 添加带样式的@用户文本
        parts.push(
          <span key={match.index} className="text-[#1677ff]">
            {match[0]}
          </span>,
        )
      } else {
        // 如果不是有效的@用户，则作为普通文本处理
        parts.push(match[0])
      }
      lastIndex = regex.lastIndex
    }
    // 添加剩余文本
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex))
    }
    return parts
  }

  return (
    <div className="flex mb-20">
      <div className="flex-1 flex items-start min-w-0">
        <XAvatar
          size={24}
          uid={uid}
          name={name}
          className="mr-10 flex-shrink-0"
        />
        <div className="flex-col mt-2 flex-1 min-w-0">
          <div className="flex justify-between">
            <span className="text-[#5C626B] text-12 leading-[1]">
              {isMe ? '我' : name}
            </span>
            <span className="text-[#959BA3] text-12 leading-[1]">
              {gmtCreate}
            </span>
          </div>
          <pre className="mt-8 text-[#262A30] text-14 break-words leading-[1.5] whitespace-pre-line">
            {renderContent(content)}
          </pre>
        </div>
      </div>
    </div>
  )
}

const Comments = ({ comments = [], id, type, onRefresh }) => {
  const { context } = useAppContext()
  const [content, setContent] = useState('')
  const [mentionUserIds, setMentionUserIds] = useState([])
  const [commentsList, setCommentsList] = useState(comments)
  const ref = useRef(null)
  const [visible, { setTrue, setFalse }] = useBoolean(false)

  const { data: options = [], loading } = useRequest(async () => {
    const { data } = await getAllUser91847()
    return data.map((el) => ({
      uid: el.uid,
      value: el.name,
      label: (
        <div className="flex items-center">
          <XAvatar
            uid={el.uid}
            name={el.name}
            className="mr-10 flex-shrink-0"
          />
          {el.name}
        </div>
      ),
    }))
  })

  const parseMentionUserIds = (text) => {
    // 匹配text中所有的@用户名称，并去options中查找对应的uid
    const regex = /@([^@\s]+)/g
    const matches = text.match(regex)
    if (matches) {
      return matches
        .map((match) => {
          const name = match.slice(1) // 去掉@符号
          const option = options.find((opt) => opt.value === name)
          return option ? option.uid : '' // 如果找到匹配的用户，返回uid，否则返回空字符串
        })
        .filter(Boolean)
    }
    return []
  }

  // 提交评论
  const { run: submit, loading: submitLoading } = useRequest(
    () =>
      addComment91881({
        id,
        type,
        uids: mentionUserIds, // 使用动态解析的@用户ID
        content,
      }),
    {
      manual: true,
      onSuccess: async () => {
        setContent('') // 清空输入
        setMentionUserIds([]) // 清空@用户列表
        try {
          const data = await onRefresh?.()
          setCommentsList(data)
        } catch (error) {
          console.error('Error fetching comments:', error)
        }
      },
    },
  )

  return (
    <Card
      title="评论"
      className="!shadow-none"
      styles={{
        header: { borderBottom: 0 },
        body: { paddingTop: 0 },
      }}
      bordered={false}
    >
      {/* 评论列表 */}
      {commentsList?.map((comment, index) => (
        <CommentItem
          key={index}
          {...comment}
          options={options}
          isMe={
            (process.env.PUBLIC_H5
              ? window.Cookies.get('userId')
              : context?.$appState.myself.loginUid) === comment.uid
          }
        />
      ))}

      {/* 评论输入框 */}
      <div className="relative mt-4 flex-col" ref={ref} onClick={setTrue}>
        <Mentions
          style={{ width: '100%', paddingBottom: visible ? '35px' : 0 }}
          placeholder="写评论，可@相关人员"
          rows={visible ? 2 : 1}
          value={content}
          onChange={(text) => {
            setContent(text)
            // 动态解析并更新@用户ID
            const userIds = parseMentionUserIds(text)
            setMentionUserIds(userIds)
          }}
          options={options}
          // filterOption={(input, option) =>
          //     option.value.toLowerCase().includes(input.toLowerCase())
          // }
        />
        {visible ? (
          <Button
            size="small"
            className="absolute right-8 bottom-12"
            type="primary"
            disabled={!content}
            loading={submitLoading}
            onClick={submit} // 绑定提交事件
            icon={
              <div className="flex items-center">
                <XmIconReact color="#fff" name="paperairplane_fill" />
              </div>
            }
          >
            发送
          </Button>
        ) : null}
      </div>
    </Card>
  )
}

export default Comments
