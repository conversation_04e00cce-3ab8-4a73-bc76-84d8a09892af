import React, { useEffect, useState } from 'react'
import Watermark from '@/components/Watermark'
import { getWatermarkConfig } from 'views/dataDashboard/config/watermark'

/**
 * 水印高阶组件
 * 为组件添加水印
 * @param {React.ComponentType} WrappedComponent - 需要添加水印的组件
 * @returns {React.ComponentType} - 添加了水印的组件
 */
const WithWatermark = (WrappedComponent) => {
  // 返回一个新的组件
  return (props) => {
    const [watermarkConfig, setWatermarkConfig] = useState({})
    const [loading, setLoading] = useState(true)

    useEffect(() => {
      // 获取水印配置
      const fetchWatermarkConfig = async () => {
        try {
          const config = await getWatermarkConfig()
          setWatermarkConfig(config)
        } catch (error) {
          console.error('获取水印配置失败:', error)
        } finally {
          setLoading(false)
        }
      }

      fetchWatermarkConfig()
    }, [])

    // 始终使用Watermark组件包装，只是在加载中时不显示水印
    return (
      <Watermark {...(loading ? {} : watermarkConfig)}>
        <WrappedComponent {...props} />
      </Watermark>
    )
  }
}

export default WithWatermark
