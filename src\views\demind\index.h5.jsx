import React, { useEffect, useRef, useState } from 'react'
import { <PERSON>vide<PERSON>, InfiniteScroll, List, Popup } from 'antd-mobile'
import { DotFill, ShaixuanLine } from '@xm/icons-ai/react-svg'
import { demindStatus as PROJECT_PHASE } from './constants'
// import { sleep } from 'antd-mobile/es/utils/sleep'
import { list91808 } from '@/apis/project3347/list91808'
// import { useRequest } from 'ahooks'
// import { session } from '@/utils'
import { useRoleStore } from '@/context/useRoleStore'
import { Tag } from 'antd'

export default () => {
  const { hasDeptSelect } = useRoleStore()

  const [visible, setVisible] = useState(false)
  const [data, setData] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const pageIndex = useRef(1)

  const [demandStatus, setDemandStatus] = useState(
    sessionStorage.demandStatus ? Number(sessionStorage.demandStatus) : '',
  )
  const [dept2, setDept2] = useState(
    sessionStorage.dept2 ? JSON.parse(sessionStorage.dept2) : '',
  )
  const [user, setUser] = useState(
    sessionStorage.user ? JSON.parse(sessionStorage.user) : '',
  )

  const loadMore = () =>
    list91808({
      demandStatus,
      deptId: dept2?.departmentId,
      preDockingUid: user?.uid,
      pageSize: 10,
      pageIndex: pageIndex.current,
    }).then((_res) => {
      const res = _res.data
      console.log(1, res)
      const _data = [...data, ...(res.rows || [])]
      setData(_data)
      setHasMore(res.total > _data.length)
      pageIndex.current += 1
    })

  const reset = () => {
    setData([])
    setHasMore(true)
    pageIndex.current = 1
  }

  useEffect(() => {
    setDemandStatus(
      sessionStorage.demandStatus ? Number(sessionStorage.demandStatus) : '',
    )
    setDept2(sessionStorage.dept2 ? JSON.parse(sessionStorage.dept2) : '')
    setUser(sessionStorage.user ? JSON.parse(sessionStorage.user) : '')
  }, [location.hash])

  return (
    <>
      <List className="[&_.adm-list-body]:bg-transparent [&_.adm-list-item]:bg-transparent [&_.adm-list-item-content]:border-none [&_.adm-list-item-content-main]:pb-0 [&_.adm-list-body]:!border-none pt-44">
        <div
          key={-1}
          className="fixed z-1 w-full top-0 left-0 bg-white h-44 flex items-center overflow-auto text-14 py-7 pl-15 pr-105"
        >
          {PROJECT_PHASE.map((item, index) => (
            <div
              key={index}
              className={`line-height-30 h-30 px-8 ${demandStatus === item.value ? 'c-[var(--xm-primary-color)] bg-[var(--xm-primary-o5-color)]' : 'c-#959BA3 bg-#F7F8F9'} rounded-4 shrink-0 mr-10`}
              onClick={() => {
                sessionStorage.setItem('demandStatus', item.value)
                setDemandStatus(item.value)
                reset()
              }}
            >
              {item.label}
            </div>
          ))}

          <div className="fixed right-0 top-0 h-44 flex items-center">
            <div
              className="w-6 h-full"
              style={{
                backgroundImage:
                  'linear-gradient(90deg, #00000000 0%, #0000000a 100%)',
              }}
            />
            <div
              className="flex items-center justify-center h-full w-72 bg-white"
              style={{
                display: hasDeptSelect ? null : 'none',
              }}
              onClick={() => {
                setVisible(true)
              }}
            >
              <span className="c-#5C626B">筛选</span>
              <ShaixuanLine size={12} className="inline-flex ml-3" />
            </div>
          </div>
        </div>

        <div className="px-12">
          {dept2 && (
            <Tag
              className="mr-12 mt-12 bg-white text-14"
              bordered={false}
              closable
              onClose={() => {
                sessionStorage.removeItem('dept2')
                setDept2(null)
                reset()
              }}
            >
              {dept2.name}
            </Tag>
          )}
          {user && (
            <Tag
              className="mt-12 bg-white text-14"
              bordered={false}
              closable
              onClose={() => {
                sessionStorage.removeItem('user')
                setUser(null)
                reset()
              }}
            >
              {user.name}
            </Tag>
          )}
        </div>

        {data.map((item, index) => (
          <List.Item key={index}>
            <div className="rounded-8 bg-white text-14">
              <div className="p-12">
                <div className="flex justify-between items-center">
                  <div className="text-16 font-500 line-clamp-1 max-w-70%">
                    {item.customerCompany}
                  </div>
                  <div className="flex-shrink-0">
                    <DotFill
                      className="inline-flex mr-8"
                      color={
                        PROJECT_PHASE.find((x) => x.value === item.demandStatus)
                          ?.color
                      }
                      size={8}
                    />
                    {
                      PROJECT_PHASE.find((x) => x.value === item.demandStatus)
                        ?.label
                    }
                  </div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">
                    需求反馈人员：
                  </div>
                  <div className="ml-4">{item.createrName}</div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">
                    需求提交时间：
                  </div>
                  <div className="ml-4">{item.gmtCreate}</div>
                </div>
                <div className="flex mt-8">
                  <div className="c-[#959BA3] flex-shrink-0">需求：</div>
                  <div className="ml-4 line-clamp-1">{item.demand}</div>
                </div>
              </div>
              <div className="h-44 border-t border-#E9ECF0 mt-12 flex items-center c-[var(--xm-primary-color)]">
                <div
                  className="w-50% h-full flex items-center justify-center"
                  onClick={() => {
                    // history.push(`/demind/detail?id=${item.id}`)
                    // openPage('/clues/detail', {
                    //   id: item.id,
                    // })
                    const url = new URL(window.location.href)
                    url.hash = `/demind/detail?id=${item.id}`
                    window.xm.openUrl({
                      url: url.toString(),
                      noDefaultMenu: 1,
                    })
                  }}
                >
                  详情
                </div>
                <Divider direction="vertical" className="!h-20" />
                <div
                  className="w-50% h-full flex items-center justify-center"
                  onClick={() => {
                    // history.push(`/demind/create?id=${item.id}`)
                    // openPage('/clues/create', {
                    //   id: item.id,
                    // })
                    const url = new URL(window.location.href)
                    url.hash = `/demind/create?id=${item.id}`
                    window.xm.openUrl({
                      url: url.toString(),
                      noDefaultMenu: 1,
                      closerefresh: 1,
                    })
                  }}
                >
                  编辑
                </div>
              </div>
            </div>
          </List.Item>
        ))}
      </List>
      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />

      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false)
        }}
        position="top"
        bodyStyle={{
          borderBottomLeftRadius: '10px',
          borderBottomRightRadius: '10px',
          minHeight: '10vh',
        }}
      >
        <div className="p-15 text-14">
          <div className="flex items-center mb-15">
            <div className="c-gray">部门：</div>
            <Tag
              className="text-14"
              bordered={false}
              closable={!!dept2}
              onClick={() => {
                window.xm
                  .selectDepartments({
                    orgid: window.Cookies.get('orgId'),
                    count: 1,
                    // selected: deptId
                    //   ? [deptId]
                    //   : [],
                  })
                  .then(function (res) {
                    // [{departmentId: '12313', name: '部门名称'}]
                    const data = res.data
                    console.log(111, data)
                    setDept2(data[0])
                    sessionStorage.setItem('dept2', JSON.stringify(data[0]))
                    reset()
                  })
              }}
              onClose={() => {
                sessionStorage.removeItem('dept2')
                setDept2(null)
                reset()
              }}
            >
              {dept2 ? dept2.name : '全部'}
            </Tag>
          </div>

          <div className="flex items-center mb-15">
            <div className="c-gray">售前负责人：</div>
            <Tag
              className="text-14"
              bordered={false}
              closable={!!user}
              onClick={() => {
                window.xm
                  .selectMembers({
                    orgId: window.Cookies.get('orgId'),
                    count: 1,
                    orgName: '',
                    // selected: value ? [value] : [],
                  })
                  .then(function (res) {
                    // [{ uid: '123123, name: '张三', mobile: '13123456789' }]
                    const data = res.data
                    // console.log(222, data)
                    // onChange({
                    //   id: data[0]?.uid,
                    //   name: data[0]?.name,
                    //   mobile: data[0]?.mobile,
                    // })
                    sessionStorage.setItem('user', JSON.stringify(data[0]))
                    setUser(data[0])
                    reset()
                  })
              }}
              onClose={() => {
                setUser(null)
                reset()
              }}
            >
              {user ? user.name : '请选择'}
            </Tag>
          </div>
        </div>
      </Popup>
    </>
  )
}
