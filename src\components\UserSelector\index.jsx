import { ProFormField } from '@ant-design/pro-components'
import { useBoolean } from 'ahooks'
import XmIconReact from '@xm/icons-ai/react-svg'
import { useAppContext } from '@xm/xapp-main'

const Tree = window.WEB_UTIL?.Tree

const UserSelector = ({ title, treeProps, onChange, value, allowClear }) => {
  const { context } = useAppContext()
  const { $appState } = context || {}
  const { orgInfo } = $appState || {}
  const {
    id: orgId = window.Cookies.get('orgId'),
    name: orgName = window.Cookies.get('orgName'),
  } = orgInfo || {}
  const [visible, { toggle, setFalse }] = useBoolean(false)
  return (
    <>
      <ProFormField
        noStyle
        width="md"
        placeholder="请选择"
        fieldProps={{
          readOnly: true,
          suffix: (
            <>
              {value?.id && !!allowClear ? (
                <div
                  onClick={(e) => {
                    e.stopPropagation()
                    onChange(null)
                  }}
                  className="flex items-center cursor-pointer"
                >
                  <XmIconReact
                    size={14}
                    color="rgba(0,0,0,0.25)"
                    name="circle_closed_fill"
                  />
                </div>
              ) : null}
              <XmIconReact color="#5C626B" name="people_added_line" />
            </>
          ),
          onClick: (e) => {
            e.stopPropagation()
            // console.error(123, {
            //   orgId,
            //   count: 1,
            //   selected: value
            //     ? [
            //       {
            //         departmentId: value?.id,
            //         name: value?.name,
            //       },
            //     ]
            //     : [],
            // })
            if (process.env.PUBLIC_H5) {
              // console.error(123, treeProps?.orgTree?.chooseDept)
              if (treeProps?.orgTree?.chooseDept) {
                // console.error(123, {
                //   orgid: orgId,
                //   count: 1,
                // })
                window.xm
                  .selectDepartments({
                    orgid: orgId,
                    count: 1,
                    // selected: value
                    //   ? [
                    //       {
                    //         departmentId: value?.id,
                    //         name: value?.name,
                    //       },
                    //     ]
                    //   : [],
                  })
                  .then(function (res) {
                    // [{departmentId: '12313', name: '部门名称'}]
                    const data = res.data
                    console.log(111, data)
                    onChange({
                      id: data[0]?.departmentId,
                      name: data[0]?.name,
                    })
                  })
              } else {
                window.xm
                  .selectMembers({
                    orgId,
                    count: 1,
                    orgName,
                    // selected: value ? [value] : [],
                  })
                  .then(function (res) {
                    // [{ uid: '123123, name: '张三', mobile: '13123456789' }]
                    const data = res.data
                    // console.log(222, data)
                    onChange({
                      id: data[0]?.uid,
                      name: data[0]?.name,
                      mobile: data[0]?.mobile,
                    })
                  })
              }
            } else {
              toggle()
            }
          },
        }}
        value={value?.name}
      />
      {visible && (
        <Tree
          isShow
          reopenWithRes={false}
          title={title}
          onConfirm={(list) => {
            onChange(list[0])
          }}
          onCancel={() => {
            setFalse()
          }}
          choosedUser={value ? [value] : undefined}
          orgId={orgId}
          orgName={orgName}
          orgTree={{ add: true, chooseDept: false, isopen: true }}
          range={1}
          {...treeProps}
        />
      )}
    </>
  )
}

export default UserSelector
