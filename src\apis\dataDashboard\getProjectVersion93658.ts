/**
 * 接口名称：获取项目版本列表
 * 接口路径：/project/statistics/version
 * 文档地址：https://yapi.shinemo.com/project/3347/interface/api/93658
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqgetProjectVersion93658 {}
export class IResgetProjectVersion93658 {
  success?: boolean
  code?: number
  msg?: string
  data?: object
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const getProjectVersion93658: Serve<
  IReqgetProjectVersion93658,
  IResgetProjectVersion93658
> = (data?, options?: HttpOptions, serviceConfig?: AxiosRequestConfig) =>
  request<IResgetProjectVersion93658>(
    {
      method: 'get',
      url: '/web-awg/baas-sale/project/statistics/version',
      data,
      ...options,
    },
    serviceConfig,
  )
