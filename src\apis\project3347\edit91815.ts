/**
 * 接口名称：需求编辑
 * 接口路径：/baas-sale/demand/edit
 * 文档地址：http://yapi.shinemo.com/project/3347/interface/api/91815
 **/

import { request, HttpOptions } from '..'
import { AxiosRequestConfig } from 'axios'
export class IReqedit91815 {
  /**
   * 需求Id
   */
  id: number
  /**
   * 需求
   */
  demand: string
  /**
   * 业务场景
   */
  businessScene: string
  /**
   * 业务痛点
   */
  businessPain: string
  /**
   * 需求实现时间要求
   */
  achieveDate?: string
  /**
   * 需求提出者身份及角色
   */
  demandProposeStaff?: string
  /**
   * 需求提出者是否具有决策权
   */
  demandProposeDecision?: string
  /**
   * 产研评审意见
   */
  productFeedback?: string
  /**
   * 生态评审意见
   */
  ecologyFeedback?: string
  /**
   * 需求状态，0：待评审，1：已评审，2：搁置，3：关闭
   */
  demandStatus: number
  /**
   * 受理状态，0：待定，1：讯盟自接，2：生态支持，3：拒绝
   */
  acceptanceStatus?: number
  /**
   * 当前登录人userId cookie中获取
   * @NotNull(message = "loginOnUserId不能为空")
   */
  loginOnUserId?: number
  /**
   * 当前登录人所在租户 cookie中获取
   */
  loginOnOrgId?: number
  /**
   * 当前登录人所在租户 cookie中获取
   */
  loginOnUsername?: string
}
export class IResedit91815 {
  code?: number
  msg?: string
  data?: boolean
}
type Serve<T, G> = (
  data?: T,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) => Promise<G>
export const edit91815: Serve<IReqedit91815, IResedit91815> = (
  data?,
  options?: HttpOptions,
  serviceConfig?: AxiosRequestConfig,
) =>
  request<IResedit91815>(
    {
      method: 'post',
      url: '/web-awg/baas-sale/demand/edit',
      data,
      ...options,
    },
    serviceConfig,
  )
