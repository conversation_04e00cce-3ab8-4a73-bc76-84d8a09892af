{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "ES2020", "ES2023"], "module": "ESNext", "jsx": "react-jsx", "noEmit": true, "strict": true, "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "moduleResolution": "bundler", "useDefineForClassFields": true, "allowImportingTsExtensions": true, "esModuleInterop": true, "paths": {"*": ["./@mf-types/*", "./src/*", "./node_modules/@types/*"], "@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["src/apis"]}