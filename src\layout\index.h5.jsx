import React, { cloneElement, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TheworkreportLine,
  TheworkreportFill,
  FolderFill,
  FolderLine,
  XiaoxiLine,
  MessageFill,
} from '@xm/icons-ai/react-svg'
// import <PERSON><PERSON> from 'js-cookie'

const menus = [
  {
    title: '线索管理',
    icon: (selected) => (selected ? <Lamp2Fill /> : <LampLine />),
    path: '/clues',
  },
  {
    title: '需求管理',
    icon: (selected) =>
      selected ? <TheworkreportFill /> : <TheworkreportLine />,
    path: '/demind',
  },
  {
    title: '文档库',
    icon: (selected) => (selected ? <FolderFill /> : <FolderLine />),
    path: '/doc',
  },
  {
    title: '智能问答',
    icon: (selected) => (selected ? <MessageFill /> : <XiaoxiLine />),
    path: '/chat',
  },
]

function LayoutH5(props) {
  const _path = location.hash.split('?')[0].slice(1)
  const [path, setPath] = React.useState(_path)

  useEffect(() => {
    // document.documentElement.style.overflow = 'hidden'
    setPath(_path)
  }, [_path])

  if (_path.split('/').length > 2) {
    // 没有 tab
    return (
      <div className="h5 overflow-auto bg-#F6F8FD !h-[calc(100%)] pb-100">
        {props.children}
      </div>
    )
  }

  return (
    <div className="h5 !h-full text-14">
      <div className="overflow-auto bg-#F6F8FD h-[calc(100%-50px)]">
        {props.children}
      </div>
      <div className="relative z-1 shrink-0 h-50 flex shadow-[0_-2px_4px_0_#0000000d] bg-white">
        {menus.map((item, index) => {
          return (
            <div
              key={index}
              className={
                'flex w-25% flex-col items-center justify-center ' +
                (path === item.path
                  ? 'c-[var(--xm-primary-color)]'
                  : 'c-[#5C626B]')
              }
              onClick={() => {
                if (item.path === '/doc') {
                  window.xm.native('opendisk', {
                    orgid: window.Cookies.get('orgId'),
                    // 1 个人
                    // 2 公共区
                    // 4 共享区
                    sharetype: 4,
                  })
                  return
                }
                // history 替换
                history.replaceState({}, '', `#${item.path}`)
                window.dispatchEvent(new Event('hashchange'))
                // location.hash = `#${item.path}`
                setPath(item.path)
              }}
            >
              {cloneElement(item.icon(path === item.path), {
                color:
                  path === item.path ? 'var(--xm-primary-color)' : '#5C626B',
                size: 24,
              })}
              <div className="text-10px">{item.title}</div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default LayoutH5
